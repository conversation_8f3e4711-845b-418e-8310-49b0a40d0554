import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../../app_properties.dart';

class MultiImagePicker extends StatefulWidget {
  final List<Uint8List> initialImages;
  final List<String> initialImageNames;
  final int mainImageIndex;
  final Function(List<Uint8List>, List<String>, int) onImagesChanged;
  final int maxImages;

  const MultiImagePicker({
    super.key,
    this.initialImages = const [],
    this.initialImageNames = const [],
    this.mainImageIndex = 0,
    required this.onImagesChanged,
    this.maxImages = 10,
  });

  @override
  State<MultiImagePicker> createState() => _MultiImagePickerState();
}

class _MultiImagePickerState extends State<MultiImagePicker> {
  late List<Uint8List> _images;
  late List<String> _imageNames;
  late int _mainImageIndex;
  final ImagePicker _picker = ImagePicker();

  // حدود Firestore - تم زيادة الحد إلى 1 ميجا
  static const int firestoreDocumentLimit = 1048576; // 1 ميجابايت
  static const int safeSizeLimit =
      1024000; // 1 ميجا (1024 كيلوبايت) - جودة عالية بدون ضغط
  int _currentTotalSize = 0;

  @override
  void initState() {
    super.initState();
    _images = List.from(widget.initialImages);
    _imageNames = List.from(widget.initialImageNames);
    _mainImageIndex = widget.mainImageIndex;
    _calculateTotalSize();
  }

  /// حساب الحجم الإجمالي للصور
  void _calculateTotalSize() {
    _currentTotalSize = 0;
    for (final image in _images) {
      // تقدير حجم base64 (حوالي 1.33 من الحجم الأصلي)
      _currentTotalSize += (image.length * 1.33).round();
    }
    debugPrint(
      '📊 الحجم الإجمالي الحالي: ${(_currentTotalSize / 1024).toStringAsFixed(1)} KB',
    );
  }

  /// التحقق من إمكانية إضافة صورة جديدة
  bool _canAddMoreImages(int newImageSize) {
    final estimatedBase64Size = (newImageSize * 1.33).round();
    final totalAfterAdd = _currentTotalSize + estimatedBase64Size;

    debugPrint(
      '📊 حجم الصورة الجديدة المقدر: ${(estimatedBase64Size / 1024).toStringAsFixed(1)} KB',
    );
    debugPrint(
      '📊 الحجم الإجمالي بعد الإضافة: ${(totalAfterAdd / 1024).toStringAsFixed(1)} KB',
    );
    debugPrint(
      '📊 الحد الآمن: ${(safeSizeLimit / 1024).toStringAsFixed(1)} KB',
    );

    return totalAfterAdd <= safeSizeLimit;
  }

  /// عرض رسالة تحذير عند الوصول للحد
  void _showSizeLimitWarning() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          '⚠️ لا يمكن إضافة المزيد من الصور!\n'
          'الحجم الحالي: ${(_currentTotalSize / 1024).toStringAsFixed(1)} KB\n'
          'الحد الأقصى: ${(safeSizeLimit / 1024).toStringAsFixed(0)} KB (جودة عالية بدون ضغط)',
          style: const TextStyle(fontSize: 14),
        ),
        backgroundColor: Colors.orange,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  Future<void> _pickImage() async {
    try {
      if (_images.length >= widget.maxImages) {
        _showMessage('الحد الأقصى ${widget.maxImages} صور');
        return;
      }

      if (kDebugMode) print('🔄 بدء عملية اختيار الصورة...');

      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (image != null) {
        final bytes = await image.readAsBytes();

        // التحقق من حجم الصورة (5MB)
        if (bytes.length > 5 * 1024 * 1024) {
          _showMessage('حجم الصورة كبير جداً. الحد الأقصى 5MB');
          return;
        }

        // التحقق من حد Firestore
        if (!_canAddMoreImages(bytes.length)) {
          _showSizeLimitWarning();
          return;
        }

        setState(() {
          _images.add(bytes);
          _imageNames.add(image.name);

          // إذا كانت هذه أول صورة، اجعلها رئيسية
          if (_images.length == 1) {
            _mainImageIndex = 0;
          }
        });

        // إعادة حساب الحجم الإجمالي
        _calculateTotalSize();
        _notifyChanges();

        if (kDebugMode) {
          print('✅ تم إضافة صورة: ${image.name}');
          print('📊 إجمالي الصور: ${_images.length}');
        }
      }
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في اختيار الصورة: $e');
      _showMessage('خطأ في اختيار الصورة');
    }
  }

  void _removeImage(int index) {
    setState(() {
      _images.removeAt(index);
      _imageNames.removeAt(index);

      // تعديل فهرس الصورة الرئيسية
      if (_mainImageIndex == index) {
        _mainImageIndex = _images.isNotEmpty ? 0 : 0;
      } else if (_mainImageIndex > index) {
        _mainImageIndex--;
      }

      // التأكد من أن الفهرس صحيح
      if (_mainImageIndex >= _images.length) {
        _mainImageIndex = _images.isNotEmpty ? _images.length - 1 : 0;
      }
    });

    // إعادة حساب الحجم الإجمالي بعد الحذف
    _calculateTotalSize();
    _notifyChanges();
  }

  void _setMainImage(int index) {
    setState(() {
      _mainImageIndex = index;
    });
    _notifyChanges();
  }

  void _notifyChanges() {
    widget.onImagesChanged(_images, _imageNames, _mainImageIndex);
  }

  void _showMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(message)));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عرض الصور المرفوعة
        if (_images.isNotEmpty) ...[
          _buildImageGallery(),
          const SizedBox(height: 16),
        ],

        // زر إضافة صورة جديدة
        _buildAddImageButton(),
      ],
    );
  }

  Widget _buildImageGallery() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'صور المنتج',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.primaryColor,
              ),
            ),
            const Spacer(),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${_images.length} من ${widget.maxImages} صورة',
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppColors.secondaryText,
                  ),
                ),
                if (_images.isNotEmpty)
                  Text(
                    'الحجم: ${(_currentTotalSize / 1024).toStringAsFixed(1)} / ${(safeSizeLimit / 1024).toStringAsFixed(1)} KB',
                    style: TextStyle(
                      fontSize: 10,
                      color: _currentTotalSize > safeSizeLimit * 0.8
                          ? AppColors.errorColor
                          : AppColors.secondaryText,
                      fontWeight: _currentTotalSize > safeSizeLimit * 0.8
                          ? FontWeight.bold
                          : FontWeight.normal,
                    ),
                  ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 12),

        // عرض الصور في شبكة
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
            childAspectRatio: 1,
          ),
          itemCount: _images.length,
          itemBuilder: (context, index) {
            return _buildImageCard(index);
          },
        ),
      ],
    );
  }

  Widget _buildImageCard(int index) {
    final isMainImage = index == _mainImageIndex;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isMainImage
              ? AppColors.primaryColor
              : AppColors.grey.withValues(alpha: 0.3),
          width: isMainImage ? 3 : 1,
        ),
      ),
      child: Stack(
        children: [
          // الصورة
          ClipRRect(
            borderRadius: BorderRadius.circular(6),
            child: Image.memory(
              _images[index],
              width: double.infinity,
              height: double.infinity,
              fit: BoxFit.cover,
            ),
          ),

          // شارة الصورة الرئيسية
          if (isMainImage)
            Positioned(
              top: 4,
              left: 4,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: AppColors.primaryColor,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Text(
                  'رئيسية',
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),

          // أزرار التحكم
          Positioned(
            top: 4,
            right: 4,
            child: Column(
              children: [
                // زر الحذف
                Container(
                  decoration: const BoxDecoration(
                    color: AppColors.errorColor,
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    icon: const Icon(
                      Icons.close,
                      color: AppColors.white,
                      size: 16,
                    ),
                    onPressed: () => _removeImage(index),
                    padding: const EdgeInsets.all(4),
                    constraints: const BoxConstraints(
                      minWidth: 24,
                      minHeight: 24,
                    ),
                  ),
                ),

                // زر تعيين كرئيسية
                if (!isMainImage) ...[
                  const SizedBox(height: 4),
                  Container(
                    decoration: const BoxDecoration(
                      color: AppColors.primaryColor,
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      icon: const Icon(
                        Icons.star,
                        color: AppColors.white,
                        size: 16,
                      ),
                      onPressed: () => _setMainImage(index),
                      padding: const EdgeInsets.all(4),
                      constraints: const BoxConstraints(
                        minWidth: 24,
                        minHeight: 24,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddImageButton() {
    final canAddMore =
        _images.length < widget.maxImages &&
        _currentTotalSize < safeSizeLimit * 0.9;

    return Container(
      width: double.infinity,
      height: _images.isEmpty ? 200 : 120,
      decoration: BoxDecoration(
        border: Border.all(
          color: canAddMore
              ? AppColors.primaryColor.withValues(alpha: 0.3)
              : AppColors.grey.withValues(alpha: 0.2),
          width: 2,
          style: BorderStyle.solid,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: InkWell(
        onTap: canAddMore ? _pickImage : null,
        borderRadius: BorderRadius.circular(8),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _images.isEmpty
                  ? Icons.cloud_upload_outlined
                  : Icons.add_photo_alternate_outlined,
              size: _images.isEmpty ? 48 : 32,
              color: canAddMore ? AppColors.primaryColor : AppColors.grey,
            ),
            const SizedBox(height: 8),
            Text(
              canAddMore
                  ? (_images.isEmpty
                        ? 'اضغط لرفع صور المنتج'
                        : 'إضافة صورة أخرى')
                  : (_images.length >= widget.maxImages
                        ? 'تم الوصول للحد الأقصى للصور'
                        : 'تم الوصول لحد حجم البيانات'),
              style: TextStyle(
                fontSize: _images.isEmpty ? 16 : 14,
                fontWeight: FontWeight.w500,
                color: canAddMore ? AppColors.primaryColor : AppColors.grey,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              canAddMore
                  ? 'PNG, JPEG (الحد الأقصى: 5MB لكل صورة)'
                  : 'حد المنتج: ${(safeSizeLimit / 1024).toStringAsFixed(0)} KB (جودة عالية)',
              style: TextStyle(
                fontSize: 12,
                color: canAddMore ? AppColors.secondaryText : AppColors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
