import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:card_swiper/card_swiper.dart';
import '../../app_properties.dart';
import '../../models/product.dart';
import '../../models/category.dart' as category_model;
import '../../api_service_mock.dart';
import '../../services/firestore_data_service.dart';
import '../product/product_page.dart';
import '../category/category_products_page.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  List<Product> _featuredProducts = [];
  List<Product> _newProducts = [];
  List<category_model.Category> _categories = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      // محاولة جلب البيانات من Firestore أولاً
      List<Product> firestoreProducts = [];
      List<Product> firestoreFeatured = [];
      List<category_model.Category> firestoreCategories = [];

      try {
        firestoreProducts = await FirestoreDataService.getProducts();
        firestoreFeatured = await FirestoreDataService.getFeaturedProducts();
        firestoreCategories = await FirestoreDataService.getCategories();

        if (kDebugMode) {
          print(
            '✅ Firestore: ${firestoreProducts.length} منتج، ${firestoreFeatured.length} مميز، ${firestoreCategories.length} فئة',
          );
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ فشل في جلب البيانات من Firestore: $e');
        }
      }

      // إذا لم توجد بيانات في Firestore، استخدم البيانات المحلية
      final futures = await Future.wait([
        firestoreFeatured.isNotEmpty
            ? Future.value(firestoreFeatured)
            : ApiService.getFeaturedProducts(),
        firestoreProducts.isNotEmpty
            ? Future.value(firestoreProducts.take(10).toList())
            : ApiService.getProducts(limit: 10),
        firestoreCategories.isNotEmpty
            ? Future.value(firestoreCategories)
            : ApiService.getCategories(),
      ]);

      if (mounted) {
        setState(() {
          _featuredProducts = futures[0] as List<Product>;
          _newProducts = futures[1] as List<Product>;
          _categories = futures[2] as List<category_model.Category>;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          // استخدام بيانات تجريبية في حالة فشل التحميل
          _categories = [];
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      body: SafeArea(
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : RefreshIndicator(
                onRefresh: _loadData,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // شريط التطبيق المخصص
                      _buildCustomAppBar(),

                      // البانر الترويجي
                      _buildPromoBanner(),

                      const SizedBox(height: 20),

                      // الفئات الرئيسية
                      _buildCategoriesSection(),

                      const SizedBox(height: 20),

                      // المنتجات المميزة
                      _buildFeaturedProductsSection(),

                      const SizedBox(height: 20),

                      // المنتجات الجديدة
                      _buildNewProductsSection(),

                      const SizedBox(height: 20),

                      // عروض خاصة
                      _buildSpecialOffersSection(),

                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
      ),
    );
  }

  Widget _buildCustomAppBar() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // شعار التطبيق
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.primaryColor,
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Icon(
              Icons.visibility,
              color: AppColors.white,
              size: 24,
            ),
          ),

          const SizedBox(width: 12),

          // اسم التطبيق والترحيب
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'مرحباً بك في ${AppConstants.appName}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.primaryText,
                  ),
                ),
                Text(
                  'اكتشف أحدث النظارات والعدسات',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.secondaryText,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPromoBanner() {
    final List<String> bannerImages = [
      'assets/images/banner1.jpg',
      'assets/images/banner2.jpg',
      'assets/images/banner3.jpg',
    ];

    return Container(
      height: 180,
      margin: const EdgeInsets.symmetric(
        horizontal: AppDimensions.marginMedium,
      ),
      child: Swiper(
        itemBuilder: (context, index) {
          return Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(
                AppDimensions.borderRadiusLarge,
              ),
              gradient: const LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [AppColors.primaryColor, AppColors.lensBlue],
              ),
            ),
            child: Stack(
              children: [
                Positioned(
                  right: 20,
                  top: 20,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'عروض خاصة',
                        style: Theme.of(context).textTheme.headlineSmall
                            ?.copyWith(
                              color: AppColors.white,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'خصم يصل إلى 50%',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              color: AppColors.white.withValues(alpha: 0.9),
                            ),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          // انتقال لصفحة العروض - سيتم تنفيذه لاحقاً
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.white,
                          foregroundColor: AppColors.primaryColor,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 8,
                          ),
                        ),
                        child: const Text('تسوق الآن'),
                      ),
                    ],
                  ),
                ),
                Positioned(
                  left: 20,
                  bottom: 20,
                  child: Icon(
                    Icons.local_offer,
                    size: 60,
                    color: AppColors.white.withValues(alpha: 0.3),
                  ),
                ),
              ],
            ),
          );
        },
        itemCount: bannerImages.length,
        pagination: const SwiperPagination(
          builder: DotSwiperPaginationBuilder(
            color: AppColors.lightGrey,
            activeColor: AppColors.primaryColor,
          ),
        ),
        autoplay: true,
        autoplayDelay: 5000,
      ),
    );
  }

  Widget _buildCategoriesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingMedium,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'تسوق حسب الفئة',
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              TextButton(
                onPressed: () {
                  // انتقال لصفحة جميع الفئات - سيتم تنفيذه لاحقاً
                },
                child: const Text('عرض الكل'),
              ),
            ],
          ),
        ),

        const SizedBox(height: 12),

        SizedBox(
          height: 100,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingMedium,
            ),
            itemCount: _categories.take(6).length,
            itemBuilder: (context, index) {
              final category = _categories[index];
              return GestureDetector(
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) =>
                          CategoryProductsPage(category: category),
                    ),
                  );
                },
                child: Container(
                  width: 80,
                  margin: const EdgeInsets.only(left: 12),
                  child: Column(
                    children: [
                      Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          color: category.primaryColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(30),
                          border: Border.all(
                            color: category.primaryColor.withValues(alpha: 0.3),
                          ),
                        ),
                        child: Icon(
                          Icons
                              .visibility, // أيقونة الفئة - سيتم تخصيصها لاحقاً
                          color: category.primaryColor,
                          size: 30,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        category.name,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildFeaturedProductsSection() {
    if (_featuredProducts.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingMedium,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'المنتجات المميزة',
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              TextButton(
                onPressed: () {
                  // انتقال لصفحة جميع المنتجات المميزة - سيتم تنفيذه لاحقاً
                },
                child: const Text('عرض الكل'),
              ),
            ],
          ),
        ),

        const SizedBox(height: 12),

        SizedBox(
          height: 280,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingMedium,
            ),
            itemCount: _featuredProducts.length,
            itemBuilder: (context, index) {
              final product = _featuredProducts[index];
              return _buildProductCard(product);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildNewProductsSection() {
    if (_newProducts.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingMedium,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'وصل حديثاً',
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              TextButton(
                onPressed: () {
                  // انتقال لصفحة المنتجات الجديدة - سيتم تنفيذه لاحقاً
                },
                child: const Text('عرض الكل'),
              ),
            ],
          ),
        ),

        const SizedBox(height: 12),

        SizedBox(
          height: 280,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingMedium,
            ),
            itemCount: _newProducts.length,
            itemBuilder: (context, index) {
              final product = _newProducts[index];
              return _buildProductCard(product);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSpecialOffersSection() {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppDimensions.marginMedium,
      ),
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.frameGold.withValues(alpha: 0.1),
            AppColors.visionGreen.withValues(alpha: 0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        border: Border.all(color: AppColors.frameGold.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'عروض خاصة للعملاء الجدد',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryText,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'احصل على خصم 25% على أول طلبية',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.secondaryText,
                  ),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    // تطبيق العرض - سيتم تنفيذه لاحقاً
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.frameGold,
                    foregroundColor: AppColors.white,
                  ),
                  child: const Text('استخدم العرض'),
                ),
              ],
            ),
          ),
          const Icon(Icons.local_offer, size: 60, color: AppColors.frameGold),
        ],
      ),
    );
  }

  Widget _buildProductCard(Product product) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => ProductPage(product: product),
          ),
        );
      },
      child: Container(
        width: 200,
        margin: const EdgeInsets.only(left: 12),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
          boxShadow: [
            BoxShadow(
              color: AppColors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة المنتج
            Container(
              height: 150,
              decoration: const BoxDecoration(
                color: AppColors.lightGrey,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(AppDimensions.borderRadiusLarge),
                  topRight: Radius.circular(AppDimensions.borderRadiusLarge),
                ),
              ),
              child: Stack(
                children: [
                  // صورة المنتج الفعلية
                  ClipRRect(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(AppDimensions.borderRadiusLarge),
                      topRight: Radius.circular(
                        AppDimensions.borderRadiusLarge,
                      ),
                    ),
                    child: product.image.isNotEmpty
                        ? Image.network(
                            product.image,
                            width: double.infinity,
                            height: 150,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                width: double.infinity,
                                height: 150,
                                color: AppColors.lightGrey,
                                child: Icon(
                                  Icons.visibility,
                                  size: 60,
                                  color: AppColors.grey.withValues(alpha: 0.5),
                                ),
                              );
                            },
                            loadingBuilder: (context, child, loadingProgress) {
                              if (loadingProgress == null) return child;
                              return Container(
                                width: double.infinity,
                                height: 150,
                                color: AppColors.lightGrey,
                                child: Center(
                                  child: CircularProgressIndicator(
                                    value:
                                        loadingProgress.expectedTotalBytes !=
                                            null
                                        ? loadingProgress
                                                  .cumulativeBytesLoaded /
                                              loadingProgress
                                                  .expectedTotalBytes!
                                        : null,
                                  ),
                                ),
                              );
                            },
                          )
                        : Container(
                            width: double.infinity,
                            height: 150,
                            color: AppColors.lightGrey,
                            child: Icon(
                              Icons.visibility,
                              size: 60,
                              color: AppColors.grey.withValues(alpha: 0.5),
                            ),
                          ),
                  ),
                  if (product.isOnSale)
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.errorColor,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'خصم',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(
                                color: AppColors.white,
                                fontWeight: FontWeight.w600,
                              ),
                        ),
                      ),
                    ),
                ],
              ),
            ),

            // معلومات المنتج
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    product.name,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    product.brand ?? product.categoryName,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.secondaryText,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Text(
                        AppConstants.formatPrice(product.finalPrice),
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.primaryColor,
                            ),
                      ),
                      if (product.hasDiscount) ...[
                        const SizedBox(width: 8),
                        Text(
                          AppConstants.formatPrice(product.originalPrice!),
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(
                                decoration: TextDecoration.lineThrough,
                                color: AppColors.secondaryText,
                              ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Icon(
                        Icons.star,
                        size: 16,
                        color: AppColors.frameGold,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        product.rating.toStringAsFixed(1),
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      Text(
                        ' (${product.reviewsCount})',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.secondaryText,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
