<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="/">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="VisionLens - متجر النظارات والعدسات اللاصقة">

  <!-- Google Sign-In Configuration -->
  <!-- ⚠️ استبدل CLIENT_ID بالقيمة الحقيقية من Google Cloud Console -->
  <meta name="google-signin-client_id" content="627749384715-lj6fcgr8pku7ajphb1859qsq894mlplb.apps.googleusercontent.com">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="VisionLens">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>VisionLens</title>
  <link rel="manifest" href="manifest.json">

  <style>
    body {
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
      font-family: 'Roboto', sans-serif;
    }

    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      flex-direction: column;
    }

    .loading-spinner {
      width: 50px;
      height: 50px;
      border: 4px solid #e3f2fd;
      border-top: 4px solid #2196f3;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .loading-text {
      margin-top: 20px;
      color: #2196f3;
      font-size: 16px;
    }
  </style>
</head>
<body>
  <div id="loading" class="loading">
    <div class="loading-spinner"></div>
    <div class="loading-text">جاري تحميل VisionLens...</div>
  </div>

  <!-- Firebase SDK -->
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-auth-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-firestore-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-storage-compat.js"></script>

  <!-- Google Sign-In -->
  <script src="https://accounts.google.com/gsi/client" async defer></script>

  <!-- Firebase Configuration -->
  <script>
    // Firebase configuration
    const firebaseConfig = {
      apiKey: "AIzaSyDkc8p_soWCQzm91z2zsYoPUyDpk21WdFw",
      authDomain: "visionlens-app-5ab70.firebaseapp.com",
      projectId: "visionlens-app-5ab70",
      storageBucket: "visionlens-app-5ab70.appspot.com",
      messagingSenderId: "************",
      appId: "1:************:web:a7cb9386ef6d04c7fe67ec"
    };

    // Initialize Firebase
    firebase.initializeApp(firebaseConfig);
    const db = firebase.firestore();

    console.log('🔥 Firebase initialized successfully');

    // دوال Firebase للاستخدام من Flutter
    window.firebaseHelpers = {
      // متغير لحفظ المنتجات والفئات والمستخدمين والطلبات والبراندات
      cachedProducts: [],
      cachedCategories: [],
      cachedUsers: [],
      cachedOrders: [],
      cachedBrands: [],
      isLoading: false,

      // جلب المنتجات مع Promise صحيح
      async getProductsAsync() {
        if (this.isLoading) {
          console.log('⏳ JavaScript: جلب المنتجات قيد التنفيذ...');
          return this.cachedProducts;
        }

        this.isLoading = true;
        try {
          console.log('🔄 JavaScript: جلب المنتجات من Firestore...');
          const snapshot = await db.collection('products').get();
          const products = [];
          snapshot.forEach(doc => {
            products.push({ id: doc.id, ...doc.data() });
          });
          console.log(`📦 JavaScript: تم جلب ${products.length} منتج`);

          // حفظ المنتجات في المتغير العام
          this.cachedProducts = products;
          window.latestProducts = products;

          console.log(`💾 JavaScript: تم حفظ ${this.cachedProducts.length} منتج في Cache`);

          return products;
        } catch (error) {
          console.error('❌ JavaScript: خطأ في جلب المنتجات:', error);
          this.cachedProducts = [];
          return [];
        } finally {
          this.isLoading = false;
        }
      },

      // جلب المنتجات (النسخة القديمة للتوافق)
      async getProducts() {
        return await this.getProductsAsync();
      },

      // جلب المنتجات المحفوظة
      getCachedProducts() {
        console.log(`📦 JavaScript: إرجاع ${this.cachedProducts.length} منتج محفوظ`);
        return this.cachedProducts;
      },

      // إضافة منتج
      async addProduct(productData) {
        try {
          console.log('🔄 JavaScript: إضافة منتج:', productData.name);
          await db.collection('products').doc(productData.id).set(productData);
          console.log('✅ JavaScript: تم إضافة المنتج بنجاح:', productData.name);

          // تحديث Cache
          await this.getProductsAsync();
          return { success: true };
        } catch (error) {
          console.error('❌ JavaScript: خطأ في إضافة المنتج:', error);
          return { success: false, error: error.message };
        }
      },

      // تعديل منتج
      async updateProduct(productData) {
        try {
          console.log('🔄 JavaScript: تعديل منتج:', productData.name);
          console.log('📝 JavaScript: بيانات التعديل:', productData);

          const result = await db.collection('products').doc(productData.id).update(productData);
          console.log('✅ JavaScript: تم تعديل المنتج بنجاح:', productData.name);
          console.log('📊 JavaScript: نتيجة التعديل:', result);

          // تحديث Cache
          await this.getProductsAsync();
          return { success: true };
        } catch (error) {
          console.error('❌ JavaScript: خطأ في تعديل المنتج:', error);
          console.error('❌ JavaScript: تفاصيل الخطأ:', error.code, error.message);
          return { success: false, error: error.message };
        }
      },

      // حذف منتج مع Promise صحيح
      deleteProduct(productId) {
        console.log('🔄 JavaScript: بدء حذف منتج:', productId);

        return new Promise(async (resolve, reject) => {
          try {
            console.log('🔗 JavaScript: مسار المنتج:', `products/${productId}`);

            // التحقق من وجود المنتج أولاً
            const docRef = db.collection('products').doc(productId);
            const docSnap = await docRef.get();

            if (!docSnap.exists) {
              console.log('⚠️ JavaScript: المنتج غير موجود:', productId);
              resolve({ success: false, error: 'المنتج غير موجود' });
              return;
            }

            console.log('📋 JavaScript: المنتج موجود، بدء الحذف...');
            await docRef.delete();
            console.log('✅ JavaScript: تم حذف المنتج بنجاح من Firebase:', productId);

            // تحديث Cache
            await this.getProductsAsync();
            console.log('� JavaScript: تم تحديث Cache بعد الحذف');

            // التحقق من الحذف
            const checkSnap = await docRef.get();
            if (!checkSnap.exists) {
              console.log('✅ JavaScript: تأكيد الحذف - المنتج لم يعد موجود');
              resolve({ success: true });
            } else {
              console.log('❌ JavaScript: فشل الحذف - المنتج ما زال موجود');
              resolve({ success: false, error: 'فشل في الحذف' });
            }

          } catch (error) {
            console.error('❌ JavaScript: خطأ في حذف المنتج:', error);
            console.error('❌ JavaScript: تفاصيل الخطأ:', error.code, error.message);
            reject({ success: false, error: error.message });
          }
        });
      },

      // === إدارة الفئات ===
      // جلب الفئات مع Promise صحيح
      getCategories() {
        console.log('🔄 JavaScript: جلب الفئات من Firestore...');

        return new Promise(async (resolve, reject) => {
          try {
            const snapshot = await db.collection('categories').get();
            const categories = [];
            snapshot.forEach(doc => {
              categories.push({ id: doc.id, ...doc.data() });
            });
            console.log(`📦 JavaScript: تم جلب ${categories.length} فئة`);

            // حفظ في Cache
            this.cachedCategories = categories;
            console.log(`💾 JavaScript: تم حفظ ${categories.length} فئة في Cache`);

            resolve(categories);
          } catch (error) {
            console.error('❌ JavaScript: خطأ في جلب الفئات:', error);
            reject(error);
          }
        });
      },

      // جلب الفئات من Cache
      getCachedCategories() {
        console.log(`📦 JavaScript: إرجاع ${this.cachedCategories ? this.cachedCategories.length : 0} فئة من Cache`);
        return this.cachedCategories || [];
      },

      // جلب المستخدمين من Cache
      getCachedUsers() {
        console.log(`👥 JavaScript: إرجاع ${this.cachedUsers ? this.cachedUsers.length : 0} مستخدم من Cache`);
        return this.cachedUsers || [];
      },

      // جلب الطلبات من Cache
      getCachedOrders() {
        console.log(`📦 JavaScript: إرجاع ${this.cachedOrders ? this.cachedOrders.length : 0} طلب من Cache`);
        return this.cachedOrders || [];
      },

      // جلب البراندات من Cache
      getCachedBrands() {
        console.log(`🏷️ JavaScript: إرجاع ${this.cachedBrands ? this.cachedBrands.length : 0} براند من Cache`);
        return this.cachedBrands || [];
      },

      // إضافة فئة
      async addCategory(categoryData) {
        try {
          console.log('🔄 JavaScript: إضافة فئة:', categoryData.name);
          await db.collection('categories').doc(categoryData.id).set(categoryData);
          console.log('✅ JavaScript: تم إضافة الفئة بنجاح:', categoryData.name);
          return { success: true };
        } catch (error) {
          console.error('❌ JavaScript: خطأ في إضافة الفئة:', error);
          return { success: false, error: error.message };
        }
      },

      // تعديل فئة
      async updateCategory(categoryData) {
        try {
          console.log('🔄 JavaScript: تعديل فئة:', categoryData.name);
          await db.collection('categories').doc(categoryData.id).update(categoryData);
          console.log('✅ JavaScript: تم تعديل الفئة بنجاح:', categoryData.name);
          return { success: true };
        } catch (error) {
          console.error('❌ JavaScript: خطأ في تعديل الفئة:', error);
          return { success: false, error: error.message };
        }
      },

      // حذف فئة
      async deleteCategory(categoryId) {
        try {
          console.log('🔄 JavaScript: حذف فئة:', categoryId);
          await db.collection('categories').doc(categoryId).delete();
          console.log('✅ JavaScript: تم حذف الفئة بنجاح:', categoryId);
          return { success: true };
        } catch (error) {
          console.error('❌ JavaScript: خطأ في حذف الفئة:', error);
          return { success: false, error: error.message };
        }
      },

      // === إدارة المستخدمين ===
      // جلب المستخدمين مع Promise صحيح
      getUsers() {
        console.log('🔄 JavaScript: جلب المستخدمين من Firestore...');

        return new Promise(async (resolve, reject) => {
          try {
            const snapshot = await db.collection('users').get();
            const users = [];
            snapshot.forEach(doc => {
              users.push({ id: doc.id, ...doc.data() });
            });
            console.log(`👥 JavaScript: تم جلب ${users.length} مستخدم`);

            // حفظ في Cache
            this.cachedUsers = users;
            console.log(`💾 JavaScript: تم حفظ ${users.length} مستخدم في Cache`);

            resolve(users);
          } catch (error) {
            console.error('❌ JavaScript: خطأ في جلب المستخدمين:', error);
            reject(error);
          }
        });
      },

      // === إدارة الطلبات ===
      // جلب الطلبات مع Promise صحيح (النسخة الجديدة)
      async getOrdersAsync() {
        if (this.isLoading) {
          console.log('⏳ JavaScript: جلب الطلبات قيد التنفيذ...');
          return this.cachedOrders;
        }

        this.isLoading = true;
        try {
          console.log('🔄 JavaScript: جلب الطلبات من Firestore...');

          const snapshot = await db.collection('orders').get();
          const orders = [];

          snapshot.forEach(doc => {
            const data = doc.data();
            orders.push({
              id: doc.id,
              ...data,
              // التأكد من تحويل التواريخ بشكل صحيح
              createdAt: data.createdAt ? data.createdAt.toDate().toISOString() : new Date().toISOString(),
              updatedAt: data.updatedAt ? data.updatedAt.toDate().toISOString() : new Date().toISOString()
            });
          });

          console.log(`📦 JavaScript: تم جلب ${orders.length} طلب من Firestore`);

          // طباعة تفاصيل أول 3 طلبات للتحقق
          for (let i = 0; i < Math.min(orders.length, 3); i++) {
            console.log(`📋 JavaScript: طلب ${i + 1}:`, {
              id: orders[i].id,
              customerName: orders[i].customerName,
              status: orders[i].status,
              total: orders[i].total
            });
          }

          // حفظ في Cache
          this.cachedOrders = orders;
          console.log(`💾 JavaScript: تم حفظ ${orders.length} طلب في Cache`);

          return orders;
        } catch (error) {
          console.error('❌ JavaScript: خطأ في جلب الطلبات:', error);
          return this.cachedOrders || [];
        } finally {
          this.isLoading = false;
        }
      },

      // جلب الطلبات (النسخة القديمة للتوافق)
      async getOrders() {
        return await this.getOrdersAsync();
      },

      // دالة اختبار الحذف المباشر
      async testDelete(productId) {
        try {
          console.log('🧪 اختبار الحذف المباشر:', productId);

          // محاولة الحذف المباشر
          await db.collection('products').doc(productId).delete();
          console.log('✅ تم الحذف المباشر بنجاح');

          // التحقق من الحذف
          const checkDoc = await db.collection('products').doc(productId).get();
          if (!checkDoc.exists) {
            console.log('✅ تأكيد: المنتج لم يعد موجود');
            return true;
          } else {
            console.log('❌ فشل: المنتج ما زال موجود');
            return false;
          }
        } catch (error) {
          console.error('❌ خطأ في الاختبار:', error);
          return false;
        }
      },

      // إضافة طلب جديد
      async addOrder(orderData) {
        try {
          console.log('🔄 JavaScript: إضافة طلب جديد:', orderData.id);
          console.log('📝 JavaScript: بيانات الطلب:', orderData);

          const docRef = await db.collection('orders').add({
            ...orderData,
            createdAt: firebase.firestore.FieldValue.serverTimestamp(),
            updatedAt: firebase.firestore.FieldValue.serverTimestamp()
          });

          console.log('✅ JavaScript: تم إضافة الطلب بنجاح مع ID:', docRef.id);
          console.log('🔗 JavaScript: رابط الطلب في Firestore:', `https://console.firebase.google.com/project/visionlens-app-5ab70/firestore/data/~2Forders~2F${docRef.id}`);

          // تحديث Cache
          await this.getOrdersAsync();
          return { success: true, id: docRef.id };
        } catch (error) {
          console.error('❌ JavaScript: خطأ في إضافة الطلب:', error);
          return { success: false, error: error.message };
        }
      },

      // تحديث حالة الطلب
      async updateOrderStatus(orderId, status) {
        try {
          console.log('🔄 JavaScript: تحديث حالة الطلب - معرف الطلب:', orderId);
          console.log('🔄 JavaScript: تحديث حالة الطلب - الحالة الجديدة:', status);
          console.log('🔄 JavaScript: نوع معرف الطلب:', typeof orderId);
          console.log('🔄 JavaScript: طول معرف الطلب:', orderId ? orderId.length : 'undefined');
          console.log('🔄 JavaScript: محتوى معرف الطلب:', JSON.stringify(orderId));

          if (!orderId || !status) {
            const errorMsg = 'معرف الطلب أو الحالة مفقود - orderId: ' + orderId + ', status: ' + status;
            console.error('❌ JavaScript:', errorMsg);
            throw new Error(errorMsg);
          }

          // التأكد من أن Firebase متاح
          if (!firebase || !firebase.firestore) {
            const errorMsg = 'Firebase غير متاح';
            console.error('❌ JavaScript:', errorMsg);
            throw new Error(errorMsg);
          }

          console.log('🔄 JavaScript: Firebase متاح، بدء التحديث...');
          const firestore = firebase.firestore();

          // تنظيف معرف الطلب من أي مسافات أو رموز غير مرغوبة
          const cleanOrderId = String(orderId).trim();
          console.log('🔄 JavaScript: معرف الطلب المنظف:', cleanOrderId);
          console.log('🔄 JavaScript: طول المعرف المنظف:', cleanOrderId.length);

          // التحقق من وجود المستند أولاً
          console.log('🔄 JavaScript: التحقق من وجود المستند...');
          const docRef = firestore.collection('orders').doc(cleanOrderId);

          try {
            const docSnapshot = await docRef.get();
            console.log('🔄 JavaScript: المستند موجود:', docSnapshot.exists);

            if (docSnapshot.exists) {
              const currentData = docSnapshot.data();
              console.log('🔄 JavaScript: بيانات المستند الحالية:', JSON.stringify(currentData, null, 2));
              console.log('🔄 JavaScript: الحالة الحالية:', currentData.status);
            } else {
              console.error('❌ JavaScript: المستند غير موجود في Firestore!');
              console.error('❌ JavaScript: معرف الطلب المستخدم:', cleanOrderId);

              // محاولة البحث عن الطلب بطرق أخرى
              console.log('🔍 JavaScript: البحث عن الطلب في جميع المستندات...');
              const allOrders = await firestore.collection('orders').get();
              console.log('🔍 JavaScript: عدد الطلبات الموجودة:', allOrders.size);

              let foundDoc = null;
              allOrders.forEach(doc => {
                console.log('🔍 JavaScript: طلب موجود:', doc.id);
                if (doc.id === cleanOrderId || doc.id === orderId) {
                  foundDoc = doc;
                  console.log('✅ JavaScript: تم العثور على الطلب:', doc.id);
                }
              });

              if (!foundDoc) {
                throw new Error('المستند غير موجود في Firestore');
              }
            }
          } catch (getError) {
            console.error('❌ JavaScript: خطأ في جلب المستند:', getError);
            throw getError;
          }

          console.log('🔄 JavaScript: تحديث المستند في Firestore...');
          const updateData = {
            status: status,
            updatedAt: firebase.firestore.FieldValue.serverTimestamp()
          };
          console.log('🔄 JavaScript: بيانات التحديث:', updateData);

          const updateResult = await docRef.update(updateData);
          console.log('🔄 JavaScript: نتيجة التحديث:', updateResult);
          console.log('✅ JavaScript: تم تحديث حالة الطلب بنجاح في Firebase');

          // التحقق من التحديث
          console.log('🔄 JavaScript: التحقق من التحديث...');
          const updatedDoc = await docRef.get();
          if (updatedDoc.exists) {
            const updatedData = updatedDoc.data();
            console.log('✅ JavaScript: البيانات بعد التحديث:', JSON.stringify(updatedData, null, 2));
            console.log('✅ JavaScript: الحالة الجديدة:', updatedData.status);
          }

          // مسح Cache وإعادة جلب البيانات
          console.log('🔄 JavaScript: مسح Cache...');
          this.cachedOrders = [];
          this.isLoading = false;

          console.log('🔄 JavaScript: إعادة جلب البيانات...');
          await this.getOrdersAsync();

          console.log('✅ JavaScript: تم تحديث Cache بعد تحديث الحالة');
          return { success: true };
        } catch (error) {
          console.error('❌ JavaScript: خطأ في تحديث حالة الطلب:', error);
          console.error('❌ JavaScript: نوع الخطأ:', typeof error);
          console.error('❌ JavaScript: رسالة الخطأ:', error.message);
          console.error('❌ JavaScript: كود الخطأ:', error.code);
          console.error('❌ JavaScript: تفاصيل الخطأ الكاملة:', JSON.stringify(error, null, 2));
          return { success: false, error: error.message };
        }
      },

      // جلب البراندات مع Promise صحيح
      getBrands() {
        console.log('🔄 JavaScript: جلب البراندات من Firestore...');

        return new Promise(async (resolve, reject) => {
          try {
            const snapshot = await db.collection('brands').get();
            const brands = [];
            snapshot.forEach(doc => {
              brands.push({ id: doc.id, ...doc.data() });
            });
            console.log(`🏷️ JavaScript: تم جلب ${brands.length} براند`);

            // حفظ في Cache
            this.cachedBrands = brands;
            console.log(`💾 JavaScript: تم حفظ ${brands.length} براند في Cache`);

            resolve(brands);
          } catch (error) {
            console.error('❌ JavaScript: خطأ في جلب البراندات:', error);
            reject(error);
          }
        });
      },

      // إضافة براند جديد
      async addBrand(brandData) {
        try {
          console.log('🔄 JavaScript: إضافة براند جديد:', brandData.name);
          await db.collection('brands').add(brandData);
          console.log('✅ JavaScript: تم إضافة البراند بنجاح:', brandData.name);
          return { success: true };
        } catch (error) {
          console.error('❌ JavaScript: خطأ في إضافة البراند:', error);
          return { success: false, error: error.message };
        }
      },

      // إضافة منتجات تجريبية مع صور
      async initializeDefaultProducts() {
        try {
          console.log('🔄 JavaScript: إضافة منتجات تجريبية...');

          const defaultProducts = [
            {
              id: '1749340935409',
              name: 'عدسات لاصقة زرقاء',
              description: 'عدسات لاصقة ملونة زرقاء عالية الجودة',
              price: 25000,
              image: 'https://images.unsplash.com/photo-1574258495973-f010dfbb5371?w=500&h=500&fit=crop',
              images: ['https://images.unsplash.com/photo-1574258495973-f010dfbb5371?w=500&h=500&fit=crop'],
              categoryId: '1',
              categoryName: 'عدسات لاصقة',
              rating: 4.5,
              reviewsCount: 25,
              inStock: true,
              stockQuantity: 50,
              type: 'contactLenses',
              createdAt: firebase.firestore.FieldValue.serverTimestamp(),
              updatedAt: firebase.firestore.FieldValue.serverTimestamp(),
            },
            {
              id: '1749345475993',
              name: 'عدسات خضراء',
              description: 'عدسات لاصقة ملونة خضراء طبيعية',
              price: 30000,
              image: 'https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=500&h=500&fit=crop',
              images: ['https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=500&h=500&fit=crop'],
              categoryId: '1',
              categoryName: 'عدسات لاصقة',
              rating: 4.8,
              reviewsCount: 42,
              inStock: true,
              stockQuantity: 30,
              type: 'contactLenses',
              createdAt: firebase.firestore.FieldValue.serverTimestamp(),
              updatedAt: firebase.firestore.FieldValue.serverTimestamp(),
            },
            {
              id: '1749346166823',
              name: 'عدسات ملونة',
              description: 'مجموعة متنوعة من العدسات الملونة',
              price: 35000,
              image: 'https://images.unsplash.com/photo-1508296695146-257a814070b4?w=500&h=500&fit=crop',
              images: ['https://images.unsplash.com/photo-1508296695146-257a814070b4?w=500&h=500&fit=crop'],
              categoryId: '1',
              categoryName: 'عدسات لاصقة',
              rating: 4.3,
              reviewsCount: 18,
              inStock: true,
              stockQuantity: 25,
              type: 'contactLenses',
              createdAt: firebase.firestore.FieldValue.serverTimestamp(),
              updatedAt: firebase.firestore.FieldValue.serverTimestamp(),
            }
          ];

          for (const product of defaultProducts) {
            await db.collection('products').doc(product.id).set(product);
            console.log(`✅ JavaScript: تم إضافة منتج: ${product.name}`);
          }

          console.log('✅ JavaScript: تم إضافة جميع المنتجات التجريبية');
          return { success: true };
        } catch (error) {
          console.error('❌ JavaScript: خطأ في إضافة المنتجات التجريبية:', error);
          return { success: false, error: error.message };
        }
      },

      // تسجيل الدخول بـ Google
      async signInWithGoogle() {
        try {
          console.log('🔄 JavaScript: تسجيل الدخول بـ Google...');
          const provider = new firebase.auth.GoogleAuthProvider();
          const result = await firebase.auth().signInWithPopup(provider);
          const user = result.user;
          console.log('✅ JavaScript: تم تسجيل الدخول بنجاح');
          return {
            success: true,
            user: {
              uid: user.uid,
              email: user.email,
              displayName: user.displayName,
              photoURL: user.photoURL
            }
          };
        } catch (error) {
          console.error('❌ JavaScript: خطأ في تسجيل الدخول:', error);
          return { success: false, error: error.message };
        }
      },

      // تسجيل الخروج
      async signOut() {
        try {
          await firebase.auth().signOut();
          console.log('✅ JavaScript: تم تسجيل الخروج');
          return { success: true };
        } catch (error) {
          console.error('❌ JavaScript: خطأ في تسجيل الخروج:', error);
          return { success: false, error: error.message };
        }
      },

      // إنشاء طلبات تجريبية مباشرة
      async createSampleOrders() {
        console.log('🔄 JavaScript: إنشاء طلبات تجريبية...');

        if (!firebase || !firebase.firestore) {
          console.error('❌ JavaScript: Firebase غير متاح');
          return { success: false, error: 'Firebase غير متاح' };
        }

        const firestore = firebase.firestore();

        const sampleOrders = [
          {
            id: '1749377694596',
            customerName: 'مدير النظام',
            customerEmail: '<EMAIL>',
            status: 'pending',
            total: 57000,
            createdAt: new Date('2025-06-08T10:14:55.241Z'),
            updatedAt: new Date(),
            items: [
              { productId: '1749340935409', productName: 'عدسات لاصقة زرقاء', quantity: 1, price: 57000 }
            ]
          },
          {
            id: 'ORD1749382116255',
            customerName: 'مدير النظام',
            customerEmail: '<EMAIL>',
            status: 'في الانتظار',
            total: 115000,
            createdAt: new Date('2025-06-08T11:28:37.070Z'),
            updatedAt: new Date(),
            items: [
              { productId: '1749345475993', productName: 'عدسات خضراء', quantity: 2, price: 57500 }
            ]
          },
          {
            id: '1749382036080',
            customerName: 'مدير النظام',
            customerEmail: '<EMAIL>',
            status: 'pending',
            total: 105000,
            createdAt: new Date('2025-06-08T11:27:16.335Z'),
            updatedAt: new Date(),
            items: [
              { productId: '1749346166823', productName: 'عدسات ملونة', quantity: 1, price: 105000 }
            ]
          },
          {
            id: 'ORD1749382346804',
            customerName: 'مدير النظام',
            customerEmail: '<EMAIL>',
            status: 'في الانتظار',
            total: 127000,
            createdAt: new Date('2025-06-08T11:32:26.866Z'),
            updatedAt: new Date(),
            items: [
              { productId: '1749371710543', productName: 'نظارات رايبات', quantity: 1, price: 127000 }
            ]
          }
        ];

        try {
          console.log('🔄 JavaScript: بدء إنشاء الطلبات في Firestore...');

          for (const order of sampleOrders) {
            const orderData = { ...order };
            delete orderData.id; // إزالة المعرف من البيانات

            console.log(`🔄 JavaScript: إنشاء طلب ${order.id}...`);
            await firestore.collection('orders').doc(order.id).set(orderData, { merge: true });
            console.log(`✅ JavaScript: تم إنشاء طلب: ${order.id}`);
          }

          console.log('✅ JavaScript: تم إنشاء جميع الطلبات التجريبية في Firestore');

          // مسح Cache وإعادة جلب البيانات
          this.cachedOrders = [];
          console.log('🔄 JavaScript: مسح Cache وإعادة جلب البيانات...');
          await this.getOrdersAsync();

          return { success: true };
        } catch (error) {
          console.error('❌ JavaScript: خطأ في إنشاء الطلبات:', error);
          console.error('❌ JavaScript: تفاصيل الخطأ:', error.message);
          console.error('❌ JavaScript: كود الخطأ:', error.code);
          return { success: false, error: error.message || error.toString() };
        }
      }
    };

    // إنشاء طلبات تجريبية مباشرة في Firestore
    window.createOrdersDirectly = async function() {
      console.log('🔄 إنشاء طلبات تجريبية مباشرة...');

      if (!firebase || !firebase.firestore) {
        console.error('❌ Firebase غير متاح');
        return;
      }

      const firestore = firebase.firestore();

      const orders = [
        {
          customerName: 'مدير النظام',
          customerEmail: '<EMAIL>',
          status: 'pending',
          total: 57000,
          createdAt: new Date(),
          updatedAt: new Date(),
          items: [{ productId: '1749340935409', productName: 'عدسات لاصقة زرقاء', quantity: 1, price: 57000 }]
        },
        {
          customerName: 'مدير النظام',
          customerEmail: '<EMAIL>',
          status: 'في الانتظار',
          total: 115000,
          createdAt: new Date(),
          updatedAt: new Date(),
          items: [{ productId: '1749345475993', productName: 'عدسات خضراء', quantity: 2, price: 57500 }]
        },
        {
          customerName: 'مدير النظام',
          customerEmail: '<EMAIL>',
          status: 'pending',
          total: 105000,
          createdAt: new Date(),
          updatedAt: new Date(),
          items: [{ productId: '1749346166823', productName: 'عدسات ملونة', quantity: 1, price: 105000 }]
        }
      ];

      try {
        for (let i = 0; i < orders.length; i++) {
          const orderId = 'ORD' + Date.now() + i;
          await firestore.collection('orders').doc(orderId).set(orders[i]);
          console.log('✅ تم إنشاء طلب:', orderId);
        }
        console.log('✅ تم إنشاء جميع الطلبات');

        // إعادة تحميل الصفحة لرؤية الطلبات الجديدة
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      } catch (error) {
        console.error('❌ خطأ في إنشاء الطلبات:', error);
      }
    };
  </script>

  <script>
    // Hide loading screen when Flutter is ready
    window.addEventListener('flutter-first-frame', function() {
      const loading = document.getElementById('loading');
      if (loading) {
        loading.style.display = 'none';
      }
    });
  </script>

  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
