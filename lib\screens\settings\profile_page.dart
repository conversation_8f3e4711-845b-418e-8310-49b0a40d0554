import 'package:flutter/material.dart';
import '../../app_properties.dart';
import '../../api_service_mock.dart';
import '../../services/app_state.dart';
import '../../services/storage_service.dart';

import '../auth/welcome_back_page.dart';
import '../admin/admin_dashboard.dart';
import '../orders/order_tracking_page.dart';
import 'edit_profile_page.dart';
import 'notifications_page.dart';
import 'prescription_page.dart';
import 'wishlist_page.dart';
import 'order_history_page.dart';
import 'address_book_page.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  final AppState _appState = AppState();
  bool _isAdminUser = false;

  @override
  void initState() {
    super.initState();
    _checkAdminStatus();
  }

  Future<void> _checkAdminStatus() async {
    final isAdmin = await _isAdmin();
    setState(() {
      _isAdminUser = isAdmin;
    });
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _appState,
      builder: (context, child) {
        return Scaffold(
          backgroundColor: AppColors.scaffoldBackground,
          appBar: AppBar(
            title: const Text('الملف الشخصي'),
            backgroundColor: AppColors.white,
            foregroundColor: AppColors.primaryText,
            elevation: 1,
            centerTitle: true,
            actions: [
              IconButton(
                icon: const Icon(Icons.edit),
                onPressed: () => _editProfile(),
              ),
            ],
          ),
          body: SingleChildScrollView(
            child: Column(
              children: [
                // معلومات المستخدم
                _buildUserHeader(),

                const SizedBox(height: 20),

                // إحصائيات سريعة
                _buildStatsSection(),

                const SizedBox(height: 20),

                // قائمة الخيارات
                _buildMenuSection(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildUserHeader() {
    final user = _appState.currentUser;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      decoration: const BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(AppDimensions.borderRadiusLarge),
          bottomRight: Radius.circular(AppDimensions.borderRadiusLarge),
        ),
      ),
      child: Column(
        children: [
          // صورة المستخدم
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              color: AppColors.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(50),
              border: Border.all(
                color: AppColors.primaryColor.withValues(alpha: 0.3),
                width: 2,
              ),
            ),
            child: user?.profileImage != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(50),
                    child: Image.network(
                      user!.profileImage!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return const Icon(
                          Icons.person,
                          size: 50,
                          color: AppColors.primaryColor,
                        );
                      },
                    ),
                  )
                : const Icon(
                    Icons.person,
                    size: 50,
                    color: AppColors.primaryColor,
                  ),
          ),

          const SizedBox(height: 16),

          // اسم المستخدم
          Text(
            user?.fullName ?? 'مستخدم VisionLens',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),

          const SizedBox(height: 4),

          // البريد الإلكتروني
          Text(
            user?.email ?? '<EMAIL>',
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: AppColors.secondaryText),
          ),

          const SizedBox(height: 8),

          // حالة التحقق
          if (user != null) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildVerificationBadge(
                  'البريد الإلكتروني',
                  user.isEmailVerified,
                ),
                const SizedBox(width: 12),
                _buildVerificationBadge('الهاتف', user.isPhoneVerified),
              ],
            ),
          ],

          const SizedBox(height: 16),

          // زر تعديل الملف الشخصي
          OutlinedButton(
            onPressed: _editProfile,
            child: const Text('تعديل الملف الشخصي'),
          ),
        ],
      ),
    );
  }

  Widget _buildVerificationBadge(String label, bool isVerified) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isVerified ? AppColors.success : AppColors.warning,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isVerified ? Icons.verified : Icons.warning,
            size: 12,
            color: AppColors.white,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: const TextStyle(
              color: AppColors.white,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsSection() {
    final cartItemsCount = _appState.cartItems.length;
    final wishlistCount = _appState.wishlistItems.length;

    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingMedium,
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              icon: Icons.shopping_cart,
              title: 'السلة',
              value: cartItemsCount.toString(),
              color: AppColors.primaryColor,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              icon: Icons.favorite,
              title: 'المفضلة',
              value: wishlistCount.toString(),
              color: AppColors.errorColor,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              icon: Icons.shopping_bag,
              title: 'الطلبات',
              value: '0', // سيتم تحديثها لاحقاً
              color: AppColors.success,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: AppColors.secondaryText),
          ),
        ],
      ),
    );
  }

  void _editProfile() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const EditProfilePage()),
    );
  }

  void _navigateToOrderHistory() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const OrderHistoryPage()),
    );
  }

  void _navigateToOrderTracking() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const OrderTrackingPage()),
    );
  }

  void _navigateToNotifications() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const NotificationsPage()),
    );
  }

  void _navigateToPrescription() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const PrescriptionPage()),
    );
  }

  void _navigateToWishlist() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const WishlistPage()),
    );
  }

  void _navigateToAddressBook() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AddressBookPage()),
    );
  }

  Widget _buildMenuSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingMedium,
      ),
      child: Column(
        children: [
          // قسم الطلبات
          _buildMenuGroup(
            title: 'الطلبات',
            items: [
              _buildMenuItem(
                icon: Icons.shopping_bag_outlined,
                title: 'طلباتي',
                subtitle: 'عرض جميع الطلبات',
                onTap: () => _navigateToOrderHistory(),
              ),
              _buildMenuItem(
                icon: Icons.favorite_outline,
                title: 'المفضلة',
                subtitle: 'المنتجات المحفوظة',
                onTap: () => _navigateToWishlist(),
              ),
              _buildMenuItem(
                icon: Icons.local_shipping_outlined,
                title: 'تتبع الشحن',
                subtitle: 'تتبع حالة الطلبات',
                onTap: () => _navigateToOrderTracking(),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // قسم الحساب
          _buildMenuGroup(
            title: 'الحساب',
            items: [
              _buildMenuItem(
                icon: Icons.location_on_outlined,
                title: 'العناوين',
                subtitle: 'إدارة عناوين التوصيل',
                onTap: () => _navigateToAddressBook(),
              ),
              _buildMenuItem(
                icon: Icons.payment_outlined,
                title: 'طرق الدفع',
                subtitle: 'إدارة البطاقات والمحافظ',
                onTap: () => _showComingSoon(),
              ),
              _buildMenuItem(
                icon: Icons.visibility_outlined,
                title: 'الوصفة الطبية',
                subtitle: 'إدارة بيانات النظر',
                onTap: () => _navigateToPrescription(),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // قسم الإدارة (للمدراء فقط)
          if (_isAdminUser) ...[
            _buildMenuGroup(
              title: 'الإدارة',
              items: [
                _buildMenuItem(
                  icon: Icons.admin_panel_settings,
                  title: 'لوحة تحكم الإدارة',
                  subtitle: 'إدارة المنتجات والطلبات والمستخدمين',
                  onTap: () => _navigateToAdminDashboard(),
                ),
              ],
            ),
            const SizedBox(height: 20),
          ],

          // قسم الإعدادات
          _buildMenuGroup(
            title: 'الإعدادات',
            items: [
              _buildMenuItem(
                icon: Icons.notifications_outlined,
                title: 'الإشعارات',
                subtitle: 'إدارة الإشعارات',
                onTap: () => _navigateToNotifications(),
              ),
              _buildMenuItem(
                icon: Icons.language_outlined,
                title: 'اللغة',
                subtitle: 'العربية',
                onTap: () => _showComingSoon(),
              ),
              _buildMenuItem(
                icon: Icons.help_outline,
                title: 'المساعدة والدعم',
                subtitle: 'الأسئلة الشائعة والدعم',
                onTap: () => _showComingSoon(),
              ),
              _buildMenuItem(
                icon: Icons.info_outline,
                title: 'حول التطبيق',
                subtitle: 'معلومات التطبيق والإصدار',
                onTap: () => _showAboutDialog(),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // زر تسجيل الخروج
          _buildLogoutButton(),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildMenuGroup({required String title, required List<Widget> items}) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingMedium),
            child: Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primaryColor,
              ),
            ),
          ),
          ...items,
        ],
      ),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: AppColors.primaryColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Icon(icon, color: AppColors.primaryColor, size: 20),
      ),
      title: Text(
        title,
        style: Theme.of(
          context,
        ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(
        subtitle,
        style: Theme.of(
          context,
        ).textTheme.bodySmall?.copyWith(color: AppColors.secondaryText),
      ),
      trailing: const Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: AppColors.grey,
      ),
      onTap: onTap,
    );
  }

  Widget _buildLogoutButton() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingMedium,
      ),
      child: OutlinedButton.icon(
        onPressed: _logout,
        icon: const Icon(Icons.logout, color: AppColors.errorColor),
        label: Text(
          'تسجيل الخروج',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: AppColors.errorColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        style: OutlinedButton.styleFrom(
          side: const BorderSide(color: AppColors.errorColor),
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
      ),
    );
  }

  void _showComingSoon() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم تنفيذ هذه الميزة قريباً'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _showAboutDialog() {
    showAboutDialog(
      context: context,
      applicationName: AppConstants.appName,
      applicationVersion: AppConstants.appVersion,
      applicationIcon: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: AppColors.primaryColor,
          borderRadius: BorderRadius.circular(30),
        ),
        child: const Icon(Icons.visibility, color: AppColors.white, size: 30),
      ),
      children: [
        const Text(AppConstants.appDescription),
        const SizedBox(height: 16),
        const Text(
          'تطبيق متجر إلكتروني متخصص في بيع النظارات والعدسات اللاصقة',
        ),
      ],
    );
  }

  void _logout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              final messenger = ScaffoldMessenger.of(context);

              navigator.pop();

              try {
                // تسجيل الخروج من API
                await ApiService.logout();

                // مسح بيانات المستخدم من AppState
                _appState.setCurrentUser(null);

                // مسح البيانات المحفوظة
                await StorageService.clearUser();

                if (mounted) {
                  navigator.pushAndRemoveUntil(
                    MaterialPageRoute(
                      builder: (context) => const WelcomeBackPage(),
                    ),
                    (route) => false,
                  );
                }
              } catch (e) {
                if (mounted) {
                  messenger.showSnackBar(
                    SnackBar(
                      content: Text('خطأ في تسجيل الخروج: $e'),
                      backgroundColor: AppColors.errorColor,
                    ),
                  );
                }
              }
            },
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }

  // التحقق من صلاحيات الإدارة
  Future<bool> _isAdmin() async {
    final currentUser = _appState.currentUser;
    if (currentUser == null) return false;

    // التحقق من أن البريد الإلكتروني في قائمة الحسابات المعتمدة
    final email = currentUser.email;
    if (email.isEmpty) return false;

    return await StorageService.isAdminEmail(email);
  }

  // الانتقال إلى لوحة تحكم الإدارة
  void _navigateToAdminDashboard() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const AdminDashboard()));
  }
}
