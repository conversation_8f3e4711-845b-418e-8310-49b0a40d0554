import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'dart:convert';
import 'dart:async';

import '../../app_properties.dart';
import '../../models/product.dart';
import '../../models/category.dart' as category_model;
import '../../api_service_mock.dart';
import '../../services/firebase_web_service.dart';
import 'multi_image_picker.dart';

class AddProductPage extends StatefulWidget {
  const AddProductPage({super.key});

  @override
  State<AddProductPage> createState() => _AddProductPageState();
}

class _AddProductPageState extends State<AddProductPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _stockController = TextEditingController();
  final _brandController = TextEditingController();
  final _skuController = TextEditingController();
  final _imageUrlController = TextEditingController();

  List<category_model.Category> _categories = [];
  List<Map<String, dynamic>> _brands = [];
  String? _selectedCategoryId;
  String? _selectedBrandId;
  bool _isLoading = false;
  bool _isActive = true;
  bool _isFeatured = false;

  // متغيرات الصور المحدثة - دعم متعدد الصور
  List<Uint8List> _productImages = [];
  List<String> _productImageNames = [];
  List<String> _productImageTypes = [];
  int _mainImageIndex = 0; // فهرس الصورة الرئيسية
  bool _useImageUpload = true; // true للرفع المباشر، false لرابط

  @override
  void initState() {
    super.initState();
    _loadCategories();
    _loadBrands();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _stockController.dispose();
    _brandController.dispose();
    _skuController.dispose();
    _imageUrlController.dispose();
    super.dispose();
  }

  Future<void> _loadCategories() async {
    try {
      if (kDebugMode) print('🔄 تحميل الفئات من Firebase...');

      // محاولة جلب الفئات من Firebase Web Service أولاً
      try {
        final categoriesData = await FirebaseWebService.getCategories();
        if (kDebugMode) {
          print('✅ تم جلب ${categoriesData.length} فئة من Firebase');
        }

        // تحويل البيانات إلى كائنات Category
        final categories = categoriesData
            .map((data) => category_model.Category.fromJson(data))
            .toList();

        setState(() {
          _categories = categories;
        });
        return;
      } catch (firebaseError) {
        if (kDebugMode) print('⚠️ فشل جلب الفئات من Firebase: $firebaseError');
      }

      // في حالة فشل Firebase، استخدم البيانات المحلية
      final categories = await ApiService.getCategories();
      if (kDebugMode) {
        print('📂 تم جلب ${categories.length} فئة من البيانات المحلية');
      }
      setState(() {
        _categories = categories;
      });
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في تحميل الفئات: $e');
      // في حالة فشل كل شيء، استخدم فئات افتراضية من PredefinedCategories
      setState(() {
        _categories = category_model.PredefinedCategories.getCategories();
      });
    }
  }

  Future<void> _loadBrands() async {
    try {
      if (kDebugMode) print('🔄 تحميل العلامات التجارية من Firebase...');

      // محاولة جلب العلامات التجارية من Firebase Web Service
      try {
        final brandsData = await FirebaseWebService.getBrands();
        if (kDebugMode) {
          print('✅ تم جلب ${brandsData.length} علامة تجارية من Firebase');
        }

        setState(() {
          _brands = brandsData;
        });
        return;
      } catch (firebaseError) {
        if (kDebugMode) {
          print('⚠️ فشل جلب العلامات التجارية من Firebase: $firebaseError');
        }
      }

      // في حالة فشل Firebase، استخدم قائمة فارغة
      setState(() {
        _brands = [];
      });
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في تحميل العلامات التجارية: $e');
      setState(() {
        _brands = [];
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: const Text('إضافة منتج جديد'),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: AppColors.white,
        elevation: 0,
        centerTitle: true,
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveProduct,
            child: const Text(
              'حفظ',
              style: TextStyle(
                color: AppColors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات أساسية
              _buildSectionCard(
                title: 'المعلومات الأساسية',
                children: [
                  _buildTextField(
                    controller: _nameController,
                    label: 'اسم المنتج',
                    hint: 'أدخل اسم المنتج',
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال اسم المنتج';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  _buildTextField(
                    controller: _descriptionController,
                    label: 'وصف المنتج',
                    hint: 'أدخل وصف تفصيلي للمنتج',
                    maxLines: 4,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال وصف المنتج';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  _buildBrandDropdown(),
                  const SizedBox(height: 16),
                  _buildTextField(
                    controller: _skuController,
                    label: 'رمز المنتج (SKU)',
                    hint: 'أدخل رمز المنتج الفريد',
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // السعر والمخزون
              _buildSectionCard(
                title: 'السعر والمخزون',
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _buildTextField(
                          controller: _priceController,
                          label: 'السعر (IQD)',
                          hint: '0',
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'يرجى إدخال السعر';
                            }
                            if (double.tryParse(value) == null) {
                              return 'يرجى إدخال رقم صحيح';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildTextField(
                          controller: _stockController,
                          label: 'الكمية المتوفرة',
                          hint: '0',
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'يرجى إدخال الكمية';
                            }
                            if (int.tryParse(value) == null) {
                              return 'يرجى إدخال رقم صحيح';
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // الصور
              _buildSectionCard(
                title: 'صور المنتج',
                children: [_buildImageSection()],
              ),

              const SizedBox(height: 20),

              // الفئة والإعدادات
              _buildSectionCard(
                title: 'الفئة والإعدادات',
                children: [
                  _buildCategoryDropdown(),
                  const SizedBox(height: 16),
                  _buildSwitchTile(
                    title: 'منتج نشط',
                    subtitle: 'سيظهر المنتج للعملاء',
                    value: _isActive,
                    onChanged: (value) => setState(() => _isActive = value),
                  ),
                  _buildSwitchTile(
                    title: 'منتج مميز',
                    subtitle: 'سيظهر في قسم المنتجات المميزة',
                    value: _isFeatured,
                    onChanged: (value) => setState(() => _isFeatured = value),
                  ),
                ],
              ),

              const SizedBox(height: 32),

              // أزرار الحفظ والإلغاء
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _isLoading
                          ? null
                          : () => Navigator.pop(context),
                      child: const Text('إلغاء'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _saveProduct,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryColor,
                        foregroundColor: AppColors.white,
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  AppColors.white,
                                ),
                              ),
                            )
                          : const Text('حفظ المنتج'),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    String? hint,
    int maxLines = 1,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        border: const OutlineInputBorder(),
        focusedBorder: const OutlineInputBorder(
          borderSide: BorderSide(color: AppColors.primaryColor),
        ),
      ),
      maxLines: maxLines,
      keyboardType: keyboardType,
      validator: validator,
    );
  }

  Widget _buildCategoryDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedCategoryId,
      decoration: const InputDecoration(
        labelText: 'الفئة',
        border: OutlineInputBorder(),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(color: AppColors.primaryColor),
        ),
      ),
      hint: const Text('اختر فئة المنتج'),
      items: _categories.map((category) {
        return DropdownMenuItem(value: category.id, child: Text(category.name));
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedCategoryId = value;
        });
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى اختيار فئة المنتج';
        }
        return null;
      },
    );
  }

  Widget _buildBrandDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedBrandId,
      decoration: const InputDecoration(
        labelText: 'العلامة التجارية',
        border: OutlineInputBorder(),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(color: AppColors.primaryColor),
        ),
      ),
      hint: const Text('اختر العلامة التجارية'),
      items: _brands.map((brand) {
        return DropdownMenuItem(
          value: brand['id'].toString(),
          child: Text(brand['name'] ?? 'غير محدد'),
        );
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedBrandId = value;
        });
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى اختيار العلامة التجارية';
        }
        return null;
      },
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return SwitchListTile(
      title: Text(title, style: const TextStyle(fontWeight: FontWeight.w500)),
      subtitle: Text(
        subtitle,
        style: const TextStyle(color: AppColors.secondaryText, fontSize: 12),
      ),
      value: value,
      onChanged: onChanged,
      activeColor: AppColors.primaryColor,
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // خيارات رفع الصورة
        Row(
          children: [
            Expanded(
              child: RadioListTile<bool>(
                title: const Text('رفع صورة مباشر'),
                subtitle: const Text('PNG, JPEG'),
                value: true,
                groupValue: _useImageUpload,
                onChanged: (value) {
                  setState(() {
                    _useImageUpload = value!;
                    if (_useImageUpload) {
                      _imageUrlController.clear();
                    } else {
                      _productImages.clear();
                      _productImageNames.clear();
                      _productImageTypes.clear();
                      _mainImageIndex = 0;
                    }
                  });
                },
                contentPadding: EdgeInsets.zero,
              ),
            ),
            Expanded(
              child: RadioListTile<bool>(
                title: const Text('رابط صورة'),
                subtitle: const Text('URL'),
                value: false,
                groupValue: _useImageUpload,
                onChanged: (value) {
                  setState(() {
                    _useImageUpload = value!;
                    if (_useImageUpload) {
                      _imageUrlController.clear();
                    } else {
                      _productImages.clear();
                      _productImageNames.clear();
                      _productImageTypes.clear();
                      _mainImageIndex = 0;
                    }
                  });
                },
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // قسم رفع الصورة المباشر
        if (_useImageUpload) ...[
          MultiImagePicker(
            initialImages: _productImages,
            initialImageNames: _productImageNames,
            mainImageIndex: _mainImageIndex,
            maxImages: 10,
            onImagesChanged: _handleImageChanges,
          ),
        ] else ...[
          _buildImageUrlSection(),
        ],
      ],
    );
  }

  // تم نقل وظيفة رفع الصور إلى MultiImagePicker

  Widget _buildImageUrlSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTextField(
          controller: _imageUrlController,
          label: 'رابط الصورة الرئيسية',
          hint: 'https://example.com/image.jpg',
          validator: (value) {
            if (!_useImageUpload && (value == null || value.trim().isEmpty)) {
              return 'يرجى إدخال رابط الصورة';
            }
            if (!_useImageUpload && value != null && value.isNotEmpty) {
              final uri = Uri.tryParse(value);
              if (uri == null || !uri.hasAbsolutePath) {
                return 'يرجى إدخال رابط صحيح';
              }
            }
            return null;
          },
        ),
        const SizedBox(height: 12),
        const Text(
          'أمثلة على روابط الصور:',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppColors.primaryText,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppColors.lightGrey.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '• https://images.unsplash.com/photo-1574258495973-f010dfbb5371?w=500&h=500',
                style: TextStyle(fontSize: 12, color: AppColors.secondaryText),
              ),
              SizedBox(height: 4),
              Text(
                '• https://example.com/product-image.jpg',
                style: TextStyle(fontSize: 12, color: AppColors.secondaryText),
              ),
              SizedBox(height: 4),
              Text(
                '• تأكد من أن الرابط يؤدي مباشرة للصورة',
                style: TextStyle(fontSize: 12, color: AppColors.warningColor),
              ),
            ],
          ),
        ),
        if (_imageUrlController.text.isNotEmpty) ...[
          const SizedBox(height: 16),
          const Text(
            'معاينة الصورة:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppColors.primaryText,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            height: 200,
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.grey.withValues(alpha: 0.3)),
              borderRadius: BorderRadius.circular(8),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(7),
              child: Image.network(
                _imageUrlController.text,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error,
                          color: AppColors.errorColor,
                          size: 40,
                        ),
                        SizedBox(height: 8),
                        Text(
                          'فشل في تحميل الصورة',
                          style: TextStyle(color: AppColors.errorColor),
                        ),
                      ],
                    ),
                  );
                },
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return const Center(child: CircularProgressIndicator());
                },
              ),
            ),
          ),
        ],
      ],
    );
  }

  // تم نقل جميع وظائف الصور إلى MultiImagePicker
  void _handleImageChanges(
    List<Uint8List> images,
    List<String> names,
    int mainIndex,
  ) {
    setState(() {
      _productImages = images;
      _productImageNames = names;
      _mainImageIndex = mainIndex;
    });
  }

  // تم نقل جميع وظائف الصور إلى MultiImagePicker

  // تم نقل جميع وظائف الصور إلى MultiImagePicker

  // تم نقل دوال ضغط الصور إلى MultiImagePicker

  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // التحقق من وجود صورة
    if (_useImageUpload && _productImages.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار صورة واحدة على الأقل للمنتج'),
          backgroundColor: AppColors.errorColor,
        ),
      );
      return;
    }

    if (!_useImageUpload && _imageUrlController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال رابط الصورة'),
          backgroundColor: AppColors.errorColor,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // البحث عن اسم الفئة
      final selectedCategory = _categories.firstWhere(
        (cat) => cat.id == _selectedCategoryId,
        orElse: () => _categories.first,
      );

      // البحث عن اسم العلامة التجارية
      String? selectedBrandName;
      if (_selectedBrandId != null) {
        final selectedBrand = _brands.firstWhere(
          (brand) => brand['id'].toString() == _selectedBrandId,
          orElse: () => <String, dynamic>{},
        );
        selectedBrandName = selectedBrand['name'];
      }

      // تحديد مصدر الصور
      String mainImageUrl = '';
      List<String> additionalImageUrls = [];

      if (_useImageUpload && _productImages.isNotEmpty) {
        // تحويل الصورة الرئيسية إلى Base64
        final mainImageBytes = _productImages[_mainImageIndex];
        final mainImageType = _productImageNames[_mainImageIndex]
            .split('.')
            .last
            .toLowerCase();
        final mimeType = mainImageType == 'png' ? 'png' : 'jpeg';
        mainImageUrl =
            'data:image/$mimeType;base64,${base64Encode(mainImageBytes)}';

        // تحويل باقي الصور إلى Base64
        for (int i = 0; i < _productImages.length; i++) {
          if (i != _mainImageIndex) {
            final imageBytes = _productImages[i];
            final imageType = _productImageNames[i]
                .split('.')
                .last
                .toLowerCase();
            final imageMimeType = imageType == 'png' ? 'png' : 'jpeg';
            final imageUrl =
                'data:image/$imageMimeType;base64,${base64Encode(imageBytes)}';
            additionalImageUrls.add(imageUrl);
          }
        }
      } else if (!_useImageUpload) {
        mainImageUrl = _imageUrlController.text.trim();
      }

      // إنشاء منتج جديد
      final newProduct = Product(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        price: double.parse(_priceController.text),
        originalPrice: double.parse(_priceController.text),
        image: mainImageUrl, // الصورة الرئيسية
        images: [mainImageUrl, ...additionalImageUrls], // جميع الصور
        categoryId: _selectedCategoryId!,
        categoryName: selectedCategory.name,
        rating: 0.0,
        reviewsCount: 0,
        inStock: int.parse(_stockController.text) > 0,
        stockQuantity: int.parse(_stockController.text),
        specifications: {},
        brand: selectedBrandName ?? _brandController.text.trim(),
        type: ProductType.eyeglasses, // افتراضي، يمكن تحسينه لاحقاً
        stock: int.parse(_stockController.text),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isFeatured: _isFeatured,
        tags: _skuController.text.trim().isEmpty
            ? null
            : _skuController.text.trim(),
      );

      // محاكاة حفظ المنتج
      await Future.delayed(const Duration(seconds: 1));

      // حفظ المنتج في Firebase والبيانات المحلية
      try {
        if (kDebugMode) {
          debugPrint('🔄 حفظ المنتج في Firebase...');
        }

        // استخدام Firebase Web Service مع كائن Product
        final success = await FirebaseWebService.addProduct(newProduct);

        if (success) {
          if (kDebugMode) {
            debugPrint('✅ تم حفظ المنتج في Firebase بنجاح');
          }
        } else {
          throw Exception('فشل في حفظ المنتج في Firebase');
        }
      } catch (e) {
        if (kDebugMode) {
          debugPrint('❌ خطأ في حفظ المنتج في Firebase: $e');
        }
        // في حالة فشل Firebase، احفظ محلياً
        try {
          await ApiService.addProduct(newProduct);
          if (kDebugMode) {
            debugPrint('📂 تم حفظ المنتج محلياً كبديل');
          }
        } catch (localError) {
          if (kDebugMode) {
            debugPrint('❌ فشل في الحفظ المحلي أيضاً: $localError');
          }
          throw Exception('فشل في حفظ المنتج');
        }
      }
      if (kDebugMode) {
        debugPrint('تم إنشاء منتج جديد: ${newProduct.name}');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إضافة المنتج بنجاح - سيتم إعادة تحميل الصفحة'),
            backgroundColor: AppColors.successColor,
            duration: Duration(seconds: 2),
          ),
        );

        // انتظار قصير ثم إعادة تحميل الصفحة لجلب البيانات المحدثة
        await Future.delayed(const Duration(seconds: 2));

        // إعادة تحميل الصفحة تلقائياً
        if (mounted) {
          Navigator.of(context).pop();
        } else {
          if (mounted) {
            Navigator.pop(context, true); // إرجاع true للإشارة إلى نجاح الإضافة
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في حفظ المنتج: $e');
      }

      // تحقق إذا كان الخطأ متعلق بإعادة التحميل (يمكن تجاهله)
      final errorString = e.toString().toLowerCase();
      final isReloadError =
          errorString.contains('reload') ||
          errorString.contains('location') ||
          errorString.contains('navigation');

      if (!isReloadError && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ أثناء حفظ المنتج'),
            backgroundColor: AppColors.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
