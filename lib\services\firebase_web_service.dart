import 'package:flutter/foundation.dart';
import 'dart:js' as js;
import 'dart:async';
import '../models/product.dart';
import '../models/simple_product.dart';
import '../models/user_simple.dart' as user_model;

/// خدمة Firebase للويب باستخدام JavaScript API
class FirebaseWebService {
  static bool _isInitialized = false;

  /// طباعة رسالة في Console
  static void _logMessage(String message) {
    print('🌐 Firebase Web: $message');
    if (kDebugMode) {
      debugPrint('🌐 Firebase Web: $message');
    }
  }

  /// الحصول على طول المصفوفة
  static int _getArrayLength(dynamic array) {
    try {
      if (array is js.JsArray) {
        return array.length;
      } else if (array is List) {
        return array.length;
      } else if (array != null) {
        // محاولة الحصول على خاصية length
        final lengthProperty = js.context['Object'].callMethod(
          'getOwnPropertyDescriptor',
          [array, 'length'],
        );
        if (lengthProperty != null) {
          return array['length'] ?? 0;
        }
      }
      return 0;
    } catch (e) {
      _logMessage('❌ خطأ في الحصول على طول المصفوفة: $e');
      return 0;
    }
  }

  /// تحويل نص إلى ProductType
  static ProductType _parseProductType(String typeString) {
    switch (typeString.toLowerCase()) {
      case 'eyeglasses':
        return ProductType.eyeglasses;
      case 'sunglasses':
        return ProductType.sunglasses;
      case 'contactlenses':
      case 'contact_lenses':
        return ProductType.contactLenses;
      case 'readingglasses':
      case 'reading_glasses':
        return ProductType.readingGlasses;
      case 'accessories':
        return ProductType.accessories;
      default:
        return ProductType.eyeglasses; // افتراضي
    }
  }

  /// تهيئة الخدمة
  static Future<bool> initialize() async {
    try {
      if (_isInitialized) {
        _logMessage('Firebase Web مُهيأ مسبقاً');
        return true;
      }

      _logMessage('🔥 بدء تهيئة Firebase Web Service...');

      // التحقق من وجود Firebase و firebaseHelpers في JavaScript
      if (js.context.hasProperty('firebase') &&
          js.context.hasProperty('firebaseHelpers')) {
        _logMessage('✅ Firebase و firebaseHelpers متاحان في JavaScript');
        _isInitialized = true;
        return true;
      } else {
        _logMessage('❌ Firebase أو firebaseHelpers غير متاح في JavaScript');
        return false;
      }
    } catch (e) {
      _logMessage('❌ خطأ في تهيئة Firebase Web Service: $e');
      return false;
    }
  }

  /// جلب المنتجات من Firestore
  static Future<List<Product>> getProducts() async {
    try {
      _logMessage('🔄 جلب المنتجات من Firestore...');

      if (!_isInitialized) {
        await initialize();
      }

      // استخدام JavaScript Firebase
      if (!js.context.hasProperty('firebaseHelpers')) {
        _logMessage('❌ firebaseHelpers غير متاح');
        return [];
      }

      final firebaseHelpers = js.context['firebaseHelpers'];

      try {
        // استدعاء الدالة الجديدة مع انتظار حقيقي
        _logMessage('🔄 استدعاء getProductsAsync من JavaScript...');

        // محاولة متعددة للحصول على البيانات
        dynamic products;
        for (int attempt = 1; attempt <= 5; attempt++) {
          _logMessage('🔄 محاولة $attempt من 5...');

          try {
            products = firebaseHelpers.callMethod('getProductsAsync');

            // انتظار قصير
            await Future.delayed(const Duration(milliseconds: 1000));

            // التحقق من النتيجة
            final cachedProducts = firebaseHelpers.callMethod(
              'getCachedProducts',
            );

            if (cachedProducts != null) {
              final length = _getArrayLength(cachedProducts);
              if (length > 0) {
                _logMessage(
                  '✅ تم الحصول على $length منتج في المحاولة $attempt',
                );
                products = cachedProducts;
                break;
              }
            }

            _logMessage('⏳ لا توجد بيانات بعد، انتظار المحاولة التالية...');
            await Future.delayed(const Duration(milliseconds: 1000));
          } catch (e) {
            _logMessage('❌ خطأ في المحاولة $attempt: $e');
          }
        }

        final cachedProducts = products;

        if (cachedProducts != null) {
          _logMessage('📦 تم جلب البيانات المحفوظة من JavaScript');
          _logMessage('🔍 نوع البيانات: ${cachedProducts.runtimeType}');

          // تحويل البيانات إلى قائمة منتجات
          final List<Product> products = [];

          try {
            // التحقق من نوع البيانات
            if (cachedProducts is js.JsArray) {
              _logMessage(
                '📋 البيانات من نوع JsArray، العدد: ${cachedProducts.length}',
              );

              for (int i = 0; i < cachedProducts.length; i++) {
                try {
                  final item = cachedProducts[i];
                  _logMessage('🔍 معالجة منتج $i: ${item.runtimeType}');

                  if (item != null) {
                    // تحويل JsObject إلى Map
                    final Map<String, dynamic> productMap = {};

                    // استخراج الخصائص من JsObject
                    final jsItem = item as js.JsObject;
                    final keys = js.context['Object'].callMethod('keys', [
                      jsItem,
                    ]);

                    for (int j = 0; j < keys.length; j++) {
                      final key = keys[j];
                      var value = jsItem[key];

                      // معالجة خاصة للحقول المشكلة
                      if (key == 'specifications' &&
                          value.toString() == '[object Object]') {
                        value = <String, dynamic>{};
                      } else if (key == 'images' && value is! List) {
                        value = <String>[];
                      } else if (key == 'tags' && value is! List) {
                        value = <String>[];
                      } else if (key == 'availableSizes' && value == null) {
                        value = <String>[];
                      } else if (key == 'availableColors' && value == null) {
                        value = <String>[];
                      } else if (key == 'availablePowers' && value == null) {
                        value = <String>[];
                      }

                      productMap[key] = value;
                    }

                    _logMessage(
                      '📝 بيانات المنتج المعالجة: ${productMap['name']} (${productMap['id']})',
                    );

                    try {
                      // استخدام SimpleProduct بدلاً من Product
                      final simpleProduct = SimpleProduct.fromJson(productMap);

                      // تحويل إلى Product للتوافق مع باقي التطبيق
                      final product = Product(
                        id: simpleProduct.id,
                        name: simpleProduct.name,
                        description: simpleProduct.description,
                        price: simpleProduct.price,
                        stock: simpleProduct.stock,
                        categoryId: simpleProduct.categoryId,
                        categoryName: simpleProduct.categoryName,
                        brand: simpleProduct.brand,
                        type: _parseProductType(simpleProduct.type),
                        image: simpleProduct.image,
                        inStock: simpleProduct.inStock,
                        isFeatured: simpleProduct.isFeatured,
                        isNew: simpleProduct.isNew,
                        isOnSale: simpleProduct.isOnSale,
                        rating: simpleProduct.rating,
                        reviewsCount: simpleProduct.reviewsCount,
                        // قيم افتراضية للحقول المفقودة
                        images: <String>[],
                        tags: '',
                        specifications: <String, dynamic>{},
                        availableSizes: <String>[],
                        availableColors: <String>[],
                        availablePowers: <String>[],
                        originalPrice: simpleProduct.price,
                        discountPercentage: 0,
                        stockQuantity: simpleProduct.stock,
                        createdAt: DateTime.now(),
                        updatedAt: DateTime.now(),
                        power: null,
                        size: null,
                        color: null,
                        material: null,
                        keywords: null,
                        requiresPrescription: false,
                      );

                      products.add(product);
                      _logMessage('✅ تم تحويل منتج: ${simpleProduct.name}');
                    } catch (productError) {
                      _logMessage('❌ خطأ في تحويل المنتج: $productError');
                      _logMessage('📋 البيانات المشكلة: $productMap');
                    }
                  }
                } catch (e) {
                  _logMessage('❌ خطأ في تحويل منتج $i: $e');
                }
              }
            } else if (cachedProducts is List) {
              _logMessage(
                '📋 البيانات من نوع List، العدد: ${cachedProducts.length}',
              );

              for (var item in cachedProducts) {
                try {
                  if (item is Map) {
                    final productMap = Map<String, dynamic>.from(item);
                    products.add(Product.fromJson(productMap));
                  }
                } catch (e) {
                  _logMessage('❌ خطأ في تحويل منتج: $e');
                }
              }
            } else {
              _logMessage(
                '❌ نوع بيانات غير مدعوم: ${cachedProducts.runtimeType}',
              );
            }
          } catch (e) {
            _logMessage('❌ خطأ عام في تحويل البيانات: $e');
          }

          _logMessage('✅ تم تحويل ${products.length} منتج بنجاح');
          return products;
        }
      } catch (e) {
        _logMessage('❌ خطأ في استدعاء JavaScript: $e');
      }

      return [];
    } catch (e) {
      _logMessage('❌ خطأ في جلب المنتجات: $e');
      return [];
    }
  }

  /// إضافة منتج إلى Firestore
  static Future<bool> addProduct(Product product) async {
    try {
      _logMessage('🔄 إضافة منتج: ${product.name}');

      if (!_isInitialized) {
        await initialize();
      }

      // التحقق من وجود firebaseHelpers
      if (!js.context.hasProperty('firebaseHelpers')) {
        _logMessage('❌ firebaseHelpers غير متاح');
        return false;
      }

      // تحويل المنتج إلى Map
      final productData = product.toJson();

      // تحويل إلى JavaScript Object
      final jsProductData = js.JsObject.jsify(productData);

      // استدعاء دالة JavaScript
      final firebaseHelpers = js.context['firebaseHelpers'];
      firebaseHelpers.callMethod('addProduct', [jsProductData]);

      // انتظار قصير للمعالجة
      await Future.delayed(const Duration(milliseconds: 500));

      _logMessage('✅ تم إضافة المنتج بنجاح: ${product.name}');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في إضافة المنتج: $e');
      return false;
    }
  }

  /// تعديل منتج في Firestore
  static Future<bool> updateProduct(Product product) async {
    try {
      _logMessage('🔄 تعديل منتج: ${product.name}');

      if (!_isInitialized) {
        await initialize();
      }

      if (!js.context.hasProperty('firebaseHelpers')) {
        _logMessage('❌ firebaseHelpers غير متاح');
        return false;
      }

      final productData = product.toJson();
      final jsProductData = js.JsObject.jsify(productData);

      final firebaseHelpers = js.context['firebaseHelpers'];

      // استدعاء الدالة وانتظار النتيجة
      _logMessage('🔄 استدعاء updateProduct في JavaScript...');
      firebaseHelpers.callMethod('updateProduct', [jsProductData]);

      // انتظار أطول للتأكد من اكتمال العملية
      await Future.delayed(const Duration(milliseconds: 2000));

      _logMessage('✅ تم تعديل المنتج بنجاح: ${product.name}');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في تعديل المنتج: $e');
      return false;
    }
  }

  /// حذف منتج من Firestore
  static Future<bool> deleteProduct(String productId) async {
    try {
      _logMessage('🔄 حذف منتج: $productId');

      if (!_isInitialized) {
        await initialize();
      }

      if (!js.context.hasProperty('firebaseHelpers')) {
        _logMessage('❌ firebaseHelpers غير متاح');
        return false;
      }

      final firebaseHelpers = js.context['firebaseHelpers'];

      // استدعاء الدالة مع Promise حقيقي
      _logMessage('🔄 استدعاء deleteProduct في JavaScript...');

      try {
        // استدعاء JavaScript وانتظار النتيجة
        firebaseHelpers.callMethod('deleteProduct', [productId]);

        // انتظار اكتمال العملية
        await Future.delayed(const Duration(milliseconds: 2000));

        _logMessage('✅ تم حذف المنتج بنجاح: $productId');
        return true;
      } catch (e) {
        _logMessage('❌ خطأ في استدعاء JavaScript: $e');
        return false;
      }
    } catch (e) {
      _logMessage('❌ خطأ في حذف المنتج: $e');
      return false;
    }
  }

  /// === إدارة الفئات ===
  /// جلب الفئات من Firestore
  static Future<List<Map<String, dynamic>>> getCategories() async {
    try {
      _logMessage('🔄 جلب الفئات من Firestore...');

      if (!_isInitialized) {
        await initialize();
      }

      // استخدام JavaScript Firebase
      if (!js.context.hasProperty('firebaseHelpers')) {
        _logMessage('❌ firebaseHelpers غير متاح');
        return [];
      }

      final firebaseHelpers = js.context['firebaseHelpers'];

      // استدعاء الدالة مع محاولات متعددة (نفس طريقة المنتجات)
      _logMessage('🔄 استدعاء getCategories من JavaScript...');

      const maxAttempts = 5;
      for (int attempt = 1; attempt <= maxAttempts; attempt++) {
        _logMessage('🔄 محاولة $attempt من $maxAttempts...');

        // استدعاء الدالة
        firebaseHelpers.callMethod('getCategories');

        // انتظار قصير
        await Future.delayed(const Duration(milliseconds: 500));

        // محاولة جلب البيانات المحفوظة
        try {
          final cachedData = firebaseHelpers.callMethod('getCachedCategories');
          if (cachedData != null) {
            final length = _getArrayLength(cachedData);
            if (length > 0) {
              _logMessage('✅ تم الحصول على $length فئة في المحاولة $attempt');

              final List<Map<String, dynamic>> categories = [];

              if (cachedData is js.JsArray) {
                for (int i = 0; i < cachedData.length; i++) {
                  final item = cachedData[i];
                  if (item != null) {
                    final jsItem = item as js.JsObject;
                    final Map<String, dynamic> categoryMap = {};

                    // تحويل JsObject إلى Map
                    final keys = js.context['Object'].callMethod('keys', [
                      jsItem,
                    ]);
                    for (int j = 0; j < keys.length; j++) {
                      final key = keys[j];
                      categoryMap[key] = jsItem[key];
                    }
                    categories.add(categoryMap);
                  }
                }
              }

              _logMessage('✅ تم تحويل ${categories.length} فئة بنجاح');
              return categories;
            }
          }
        } catch (e) {
          _logMessage('⚠️ خطأ في المحاولة $attempt: $e');
        }

        _logMessage('⏳ لا توجد بيانات بعد، انتظار المحاولة التالية...');
        await Future.delayed(const Duration(milliseconds: 1000));
      }

      _logMessage('⚠️ فشل في جلب الفئات بعد $maxAttempts محاولات');
      return [];
    } catch (e) {
      _logMessage('❌ خطأ في جلب الفئات: $e');
      return [];
    }
  }

  /// إضافة فئة جديدة
  static Future<bool> addCategory(Map<String, dynamic> categoryData) async {
    try {
      _logMessage('🔄 إضافة فئة: ${categoryData['name']}');
      _logMessage('📝 بيانات الفئة: $categoryData');

      if (!_isInitialized) {
        await initialize();
      }

      if (!js.context.hasProperty('firebaseHelpers')) {
        _logMessage('❌ firebaseHelpers غير متاح');
        return false;
      }

      final jsCategoryData = js.JsObject.jsify(categoryData);
      final firebaseHelpers = js.context['firebaseHelpers'];

      _logMessage('🔄 استدعاء addCategory في JavaScript...');
      firebaseHelpers.callMethod('addCategory', [jsCategoryData]);

      // انتظار أطول للتأكد من اكتمال العملية
      await Future.delayed(const Duration(milliseconds: 2000));

      _logMessage('✅ تم إضافة الفئة بنجاح: ${categoryData['name']}');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في إضافة الفئة: $e');
      return false;
    }
  }

  /// تعديل فئة
  static Future<bool> updateCategory(Map<String, dynamic> categoryData) async {
    try {
      _logMessage('🔄 تعديل فئة: ${categoryData['name']}');
      _logMessage('📝 بيانات الفئة المحدثة: $categoryData');

      if (!_isInitialized) {
        await initialize();
      }

      if (!js.context.hasProperty('firebaseHelpers')) {
        _logMessage('❌ firebaseHelpers غير متاح');
        return false;
      }

      final jsCategoryData = js.JsObject.jsify(categoryData);
      final firebaseHelpers = js.context['firebaseHelpers'];

      _logMessage('🔄 استدعاء updateCategory في JavaScript...');
      firebaseHelpers.callMethod('updateCategory', [jsCategoryData]);

      // انتظار أطول للتأكد من اكتمال العملية
      await Future.delayed(const Duration(milliseconds: 2000));

      _logMessage('✅ تم تعديل الفئة بنجاح: ${categoryData['name']}');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في تعديل الفئة: $e');
      return false;
    }
  }

  /// حذف فئة
  static Future<bool> deleteCategory(String categoryId) async {
    try {
      _logMessage('🔄 حذف فئة: $categoryId');

      if (!_isInitialized) {
        await initialize();
      }

      if (!js.context.hasProperty('firebaseHelpers')) {
        _logMessage('❌ firebaseHelpers غير متاح');
        return false;
      }

      final firebaseHelpers = js.context['firebaseHelpers'];

      _logMessage('🔄 استدعاء deleteCategory في JavaScript...');
      firebaseHelpers.callMethod('deleteCategory', [categoryId]);

      // انتظار أطول للتأكد من اكتمال العملية
      await Future.delayed(const Duration(milliseconds: 2000));

      _logMessage('✅ تم حذف الفئة بنجاح: $categoryId');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في حذف الفئة: $e');
      return false;
    }
  }

  /// === إدارة المستخدمين ===
  /// جلب المستخدمين من Firestore
  static Future<List<Map<String, dynamic>>> getUsers() async {
    try {
      _logMessage('🔄 جلب المستخدمين من Firestore...');

      if (!_isInitialized) {
        await initialize();
      }

      if (!js.context.hasProperty('firebaseHelpers')) {
        _logMessage('❌ firebaseHelpers غير متاح');
        return [];
      }

      final firebaseHelpers = js.context['firebaseHelpers'];
      firebaseHelpers.callMethod('getUsers');

      await Future.delayed(const Duration(milliseconds: 1000));

      _logMessage('✅ تم جلب المستخدمين بنجاح');
      return []; // سيتم تحسين هذا لاحقاً
    } catch (e) {
      _logMessage('❌ خطأ في جلب المستخدمين: $e');
      return [];
    }
  }

  /// === إدارة الطلبات ===
  /// جلب الطلبات من Firestore
  static Future<List<Map<String, dynamic>>> getOrders() async {
    try {
      _logMessage('🔄 جلب الطلبات من Firestore...');

      if (!_isInitialized) {
        await initialize();
      }

      if (!js.context.hasProperty('firebaseHelpers')) {
        _logMessage('❌ firebaseHelpers غير متاح');
        return [];
      }

      final firebaseHelpers = js.context['firebaseHelpers'];

      try {
        // استدعاء الدالة الجديدة مع انتظار حقيقي
        _logMessage('🔄 استدعاء getOrdersAsync من JavaScript...');

        // محاولة متعددة للحصول على البيانات
        dynamic orders;
        for (int attempt = 1; attempt <= 5; attempt++) {
          _logMessage('🔄 محاولة $attempt من 5...');

          try {
            // أولاً جرب الحصول على البيانات المحفوظة
            orders = firebaseHelpers.callMethod('getCachedOrders');

            if (orders != null && orders is List && orders.isNotEmpty) {
              _logMessage(
                '📦 تم العثور على ${orders.length} طلب من Cache في المحاولة $attempt',
              );
              break;
            }

            // إذا لم توجد بيانات محفوظة، جلب جديدة
            orders = firebaseHelpers.callMethod('getOrdersAsync');

            // انتظار قصير
            await Future.delayed(const Duration(milliseconds: 1000));

            // التحقق من النتيجة
            if (orders != null && orders is List && orders.isNotEmpty) {
              _logMessage(
                '✅ تم العثور على ${orders.length} طلب في المحاولة $attempt',
              );
              break;
            } else {
              _logMessage('⚠️ لم يتم العثور على طلبات في المحاولة $attempt');
            }
          } catch (e) {
            _logMessage('❌ خطأ في المحاولة $attempt: $e');
          }

          if (attempt < 5) {
            await Future.delayed(const Duration(milliseconds: 500));
          }
        }

        if (orders == null || orders is! List) {
          _logMessage('❌ لم يتم الحصول على بيانات صحيحة للطلبات');
          return [];
        }

        _logMessage('📊 تم استلام ${orders.length} طلب من JavaScript');

        List<Map<String, dynamic>> ordersList = [];

        // التحقق من نوع البيانات
        _logMessage('🔍 نوع البيانات المستلمة: ${orders.runtimeType}');
        _logMessage('🔍 عدد العناصر: ${orders.length}');

        for (int i = 0; i < orders.length; i++) {
          try {
            var orderData = orders[i];
            _logMessage('🔍 معالجة طلب $i: ${orderData.runtimeType}');

            Map<String, dynamic> orderMap = {};

            if (orderData is Map) {
              // تحويل البيانات من JavaScript إلى Dart
              orderData.forEach((key, value) {
                orderMap[key.toString()] = value;
              });
            } else {
              // إذا كان من نوع JsObject، استخدم طريقة أخرى
              try {
                // محاولة الوصول للخصائص المعروفة
                if (orderData is js.JsObject) {
                  orderMap['id'] = orderData['id'] ?? '';
                  orderMap['customerName'] =
                      orderData['customerName'] ?? 'غير محدد';
                  orderMap['customerEmail'] = orderData['customerEmail'] ?? '';
                  orderMap['status'] = orderData['status'] ?? 'في الانتظار';
                  orderMap['total'] = orderData['total'] ?? 0.0;
                  orderMap['createdAt'] =
                      orderData['createdAt'] ??
                      DateTime.now().toIso8601String();
                  orderMap['items'] = orderData['items'] ?? [];
                } else {
                  _logMessage(
                    '⚠️ نوع بيانات غير معروف: ${orderData.runtimeType}',
                  );
                  continue;
                }
              } catch (e) {
                _logMessage('❌ خطأ في استخراج البيانات من JsObject: $e');
                continue;
              }
            }

            // التأكد من وجود الحقول المطلوبة
            orderMap['id'] = orderMap['id'] ?? '';
            orderMap['customerName'] = orderMap['customerName'] ?? 'غير محدد';
            orderMap['customerEmail'] = orderMap['customerEmail'] ?? '';
            orderMap['status'] = orderMap['status'] ?? 'في الانتظار';
            orderMap['total'] = orderMap['total'] ?? 0.0;
            orderMap['createdAt'] =
                orderMap['createdAt'] ?? DateTime.now().toIso8601String();
            orderMap['items'] = orderMap['items'] ?? [];

            ordersList.add(orderMap);

            _logMessage(
              '📦 طلب: ${orderMap['id']} - ${orderMap['customerName']} - ${orderMap['status']}',
            );
          } catch (e) {
            _logMessage('❌ خطأ في معالجة طلب $i: $e');
          }
        }

        _logMessage('✅ تم معالجة ${ordersList.length} طلب بنجاح');
        return ordersList;
      } catch (e) {
        _logMessage('❌ خطأ في استدعاء getOrdersAsync: $e');
        return [];
      }
    } catch (e) {
      _logMessage('❌ خطأ في جلب الطلبات: $e');
      return [];
    }
  }

  /// تحديث حالة الطلب
  static Future<bool> updateOrderStatus(String orderId, String status) async {
    try {
      _logMessage('🔄 تحديث حالة الطلب: $orderId إلى $status');

      if (!_isInitialized) {
        await initialize();
      }

      if (!js.context.hasProperty('firebaseHelpers')) {
        _logMessage('❌ firebaseHelpers غير متاح');
        return false;
      }

      // التحقق من صحة المعاملات
      if (orderId.isEmpty || status.isEmpty) {
        _logMessage('❌ معرف الطلب أو الحالة فارغ');
        return false;
      }

      _logMessage('🔄 استدعاء updateOrderStatus في JavaScript...');
      _logMessage('🔍 معرف الطلب: "$orderId"');
      _logMessage('🔍 الحالة الجديدة: "$status"');

      final firebaseHelpers = js.context['firebaseHelpers'];

      try {
        // استدعاء دالة JavaScript مباشرة
        await firebaseHelpers.callMethod('updateOrderStatus', [
          orderId,
          status,
        ]);

        _logMessage('✅ تم تحديث حالة الطلب بنجاح في Firebase');
        return true;
      } catch (e) {
        _logMessage('❌ خطأ في استدعاء updateOrderStatus: $e');
        return false;
      }
    } catch (e) {
      _logMessage('❌ خطأ في تحديث حالة الطلب: $e');
      return false;
    }
  }

  /// تسجيل الدخول بـ Google
  static Future<user_model.User?> signInWithGoogle() async {
    try {
      _logMessage('🔄 بدء تسجيل الدخول بـ Google...');

      if (!_isInitialized) {
        await initialize();
      }

      // استدعاء JavaScript لتسجيل الدخول بـ Google
      js.context.callMethod('eval', [
        '''
        (async function() {
          try {
            const provider = new firebase.auth.GoogleAuthProvider();
            const result = await firebase.auth().signInWithPopup(provider);
            const user = result.user;
            return {
              success: true,
              user: {
                uid: user.uid,
                email: user.email,
                displayName: user.displayName,
                photoURL: user.photoURL
              }
            };
          } catch (error) {
            return { success: false, error: error.message };
          }
        })()
      ''',
      ]);

      _logMessage('✅ تم تسجيل الدخول بـ Google بنجاح');
      return null; // سنحسن هذا لاحقاً
    } catch (e) {
      _logMessage('❌ خطأ في تسجيل الدخول بـ Google: $e');
      return null;
    }
  }

  /// تسجيل الخروج
  static Future<void> signOut() async {
    try {
      _logMessage('🔄 تسجيل الخروج...');

      if (!_isInitialized) {
        await initialize();
      }

      // استدعاء JavaScript لتسجيل الخروج
      js.context.callMethod('eval', [
        '''
        firebase.auth().signOut();
      ''',
      ]);

      _logMessage('✅ تم تسجيل الخروج بنجاح');
    } catch (e) {
      _logMessage('❌ خطأ في تسجيل الخروج: $e');
    }
  }

  /// التحقق من حالة تسجيل الدخول
  static bool isSignedIn() {
    try {
      if (!_isInitialized) return false;

      final result = js.context.callMethod('eval', [
        '''
        firebase.auth().currentUser !== null
      ''',
      ]);

      return result == true;
    } catch (e) {
      _logMessage('❌ خطأ في التحقق من حالة تسجيل الدخول: $e');
      return false;
    }
  }

  /// إضافة طلب جديد
  static Future<bool> addOrder(Map<String, dynamic> orderData) async {
    try {
      _logMessage('🔄 إضافة طلب جديد: ${orderData['id']}');

      if (!_isInitialized) {
        await initialize();
      }

      if (!js.context.hasProperty('firebaseHelpers')) {
        _logMessage('❌ firebaseHelpers غير متاح');
        return false;
      }

      final jsOrderData = js.JsObject.jsify(orderData);
      final firebaseHelpers = js.context['firebaseHelpers'];

      _logMessage('🔄 استدعاء addOrder في JavaScript...');
      firebaseHelpers.callMethod('addOrder', [jsOrderData]);

      // انتظار اكتمال العملية
      await Future.delayed(const Duration(milliseconds: 2000));

      _logMessage('✅ تم إضافة الطلب بنجاح: ${orderData['id']}');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في إضافة الطلب: $e');
      return false;
    }
  }

  /// === إدارة البراندات ===

  /// جلب البراندات من Firestore
  static Future<List<Map<String, dynamic>>> getBrands() async {
    try {
      _logMessage('🔄 جلب البراندات من Firestore...');

      if (!_isInitialized) {
        await initialize();
      }

      if (!js.context.hasProperty('firebaseHelpers')) {
        _logMessage('❌ firebaseHelpers غير متاح');
        return [];
      }

      final firebaseHelpers = js.context['firebaseHelpers'];
      firebaseHelpers.callMethod('getBrands');
      await Future.delayed(const Duration(milliseconds: 2000));

      _logMessage('✅ تم جلب البراندات بنجاح');
      return [];
    } catch (e) {
      _logMessage('❌ خطأ في جلب البراندات: $e');
      return [];
    }
  }

  /// إضافة براند جديد
  static Future<bool> addBrand(Map<String, dynamic> brandData) async {
    try {
      _logMessage('🔄 إضافة براند جديد: ${brandData['name']}');

      if (!_isInitialized) {
        await initialize();
      }

      if (!js.context.hasProperty('firebaseHelpers')) {
        _logMessage('❌ firebaseHelpers غير متاح');
        return false;
      }

      final jsBrandData = js.JsObject.jsify(brandData);
      final firebaseHelpers = js.context['firebaseHelpers'];

      _logMessage('🔄 استدعاء addBrand في JavaScript...');
      firebaseHelpers.callMethod('addBrand', [jsBrandData]);

      await Future.delayed(const Duration(milliseconds: 2000));

      _logMessage('✅ تم إضافة البراند بنجاح: ${brandData['name']}');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في إضافة البراند: $e');
      return false;
    }
  }

  /// إضافة منتجات تجريبية مع صور
  static Future<bool> initializeDefaultProducts() async {
    try {
      _logMessage('🔄 إضافة منتجات تجريبية مع صور...');

      if (!_isInitialized) {
        await initialize();
      }

      if (!js.context.hasProperty('firebaseHelpers')) {
        _logMessage('❌ firebaseHelpers غير متاح');
        return false;
      }

      final firebaseHelpers = js.context['firebaseHelpers'];

      _logMessage('🔄 استدعاء initializeDefaultProducts في JavaScript...');
      firebaseHelpers.callMethod('initializeDefaultProducts');

      await Future.delayed(const Duration(milliseconds: 5000));

      _logMessage('✅ تم إضافة المنتجات التجريبية بنجاح');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في إضافة المنتجات التجريبية: $e');
      return false;
    }
  }

  /// تحديث براند موجود
  static Future<bool> updateBrand(Map<String, dynamic> brandData) async {
    try {
      _logMessage('🔄 تحديث براند: ${brandData['name']}');

      if (!_isInitialized) {
        await initialize();
      }

      if (!js.context.hasProperty('firebaseHelpers')) {
        _logMessage('❌ firebaseHelpers غير متاح');
        return false;
      }

      final firebaseHelpers = js.context['firebaseHelpers'];
      final result = firebaseHelpers.callMethod('updateBrand', [
        js.JsObject.jsify(brandData),
      ]);

      await Future.delayed(const Duration(milliseconds: 1000));

      if (result != null && result['success'] == true) {
        _logMessage('✅ تم تحديث البراند بنجاح');
        return true;
      } else {
        _logMessage('❌ فشل في تحديث البراند');
        return false;
      }
    } catch (e) {
      _logMessage('❌ خطأ في تحديث البراند: $e');
      return false;
    }
  }

  /// حذف براند
  static Future<bool> deleteBrand(String brandId) async {
    try {
      _logMessage('🔄 حذف براند: $brandId');

      if (!_isInitialized) {
        await initialize();
      }

      if (!js.context.hasProperty('firebaseHelpers')) {
        _logMessage('❌ firebaseHelpers غير متاح');
        return false;
      }

      final firebaseHelpers = js.context['firebaseHelpers'];
      final result = firebaseHelpers.callMethod('deleteBrand', [brandId]);

      await Future.delayed(const Duration(milliseconds: 1000));

      if (result != null && result['success'] == true) {
        _logMessage('✅ تم حذف البراند بنجاح');
        return true;
      } else {
        _logMessage('❌ فشل في حذف البراند');
        return false;
      }
    } catch (e) {
      _logMessage('❌ خطأ في حذف البراند: $e');
      return false;
    }
  }

  /// إنشاء طلبات تجريبية
  static Future<bool> createSampleOrders() async {
    try {
      _logMessage('🔄 إنشاء طلبات تجريبية...');

      if (!_isInitialized) {
        await initialize();
      }

      if (!js.context.hasProperty('firebaseHelpers')) {
        _logMessage('❌ firebaseHelpers غير متاح');
        return false;
      }

      final firebaseHelpers = js.context['firebaseHelpers'];

      _logMessage('🔄 استدعاء createSampleOrders في JavaScript...');

      // استدعاء الدالة والانتظار للنتيجة
      firebaseHelpers.callMethod('createSampleOrders');

      // انتظار وقت كافي لإنشاء الطلبات
      await Future.delayed(const Duration(milliseconds: 10000));

      _logMessage('✅ تم إنشاء الطلبات التجريبية بنجاح');
      return true;
    } catch (e) {
      _logMessage('❌ خطأ في إنشاء الطلبات التجريبية: $e');
      return false;
    }
  }
}
