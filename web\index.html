<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="VisionLens - متجر النظارات والعدسات اللاصقة">

  <!-- Google Sign-In Configuration -->
  <!-- ⚠️ استبدل CLIENT_ID بالقيمة الحقيقية من Google Cloud Console -->
  <meta name="google-signin-client_id" content="627749384715-lj6fcgr8pku7ajphb1859qsq894mlplb.apps.googleusercontent.com">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="VisionLens">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>VisionLens</title>
  <link rel="manifest" href="manifest.json">

  <style>
    body {
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
      font-family: 'Roboto', sans-serif;
    }

    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      flex-direction: column;
    }

    .loading-spinner {
      width: 50px;
      height: 50px;
      border: 4px solid #e3f2fd;
      border-top: 4px solid #2196f3;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .loading-text {
      margin-top: 20px;
      color: #2196f3;
      font-size: 16px;
    }
  </style>
</head>
<body>
  <div id="loading" class="loading">
    <div class="loading-spinner"></div>
    <div class="loading-text">جاري تحميل VisionLens...</div>
  </div>

  <!-- Firebase SDK -->
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-auth-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-firestore-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-storage-compat.js"></script>

  <!-- Google Sign-In -->
  <script src="https://accounts.google.com/gsi/client" async defer></script>

  <!-- Firebase Configuration -->
  <script>
    // Firebase configuration
    const firebaseConfig = {
      apiKey: "AIzaSyDkc8p_soWCQzm91z2zsYoPUyDpk21WdFw",
      authDomain: "visionlens-app-5ab70.firebaseapp.com",
      projectId: "visionlens-app-5ab70",
      storageBucket: "visionlens-app-5ab70.appspot.com",
      messagingSenderId: "************",
      appId: "1:************:web:a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0",
      measurementId: "G-XXXXXXXXXX"
    };

    // Initialize Firebase
    firebase.initializeApp(firebaseConfig);

    // إضافة firebaseHelpers للتطبيق
    window.firebaseHelpers = {
      // دوال مساعدة للتفاعل مع Firebase
      getFirestore: function() {
        return firebase.firestore();
      },
      getAuth: function() {
        return firebase.auth();
      },
      getStorage: function() {
        return firebase.storage();
      },
      // دالة لجلب المنتجات
      getProducts: async function() {
        try {
          const db = firebase.firestore();
          const snapshot = await db.collection('products').get();
          const products = [];
          snapshot.forEach(doc => {
            products.push({ id: doc.id, ...doc.data() });
          });
          console.log('🛍️ تم جلب', products.length, 'منتج من Firestore');
          return products;
        } catch (error) {
          console.error('❌ خطأ في جلب المنتجات:', error);
          return [];
        }
      },
      // دالة لجلب الفئات
      getCategories: async function() {
        try {
          const db = firebase.firestore();
          const snapshot = await db.collection('categories').get();
          const categories = [];
          snapshot.forEach(doc => {
            categories.push({ id: doc.id, ...doc.data() });
          });
          console.log('📂 تم جلب', categories.length, 'فئة من Firestore');
          return categories;
        } catch (error) {
          console.error('❌ خطأ في جلب الفئات:', error);
          return [];
        }
      },
      // دالة لجلب الطلبات
      getOrders: async function() {
        try {
          const db = firebase.firestore();
          const snapshot = await db.collection('orders').get();
          const orders = [];
          snapshot.forEach(doc => {
            orders.push({ id: doc.id, ...doc.data() });
          });
          console.log('📦 تم جلب', orders.length, 'طلب من Firestore');
          return orders;
        } catch (error) {
          console.error('❌ خطأ في جلب الطلبات:', error);
          return [];
        }
      },
      // دالة لإضافة منتج
      addProduct: async function(product) {
        try {
          const db = firebase.firestore();
          const docRef = await db.collection('products').add(product);
          console.log('✅ تم إضافة منتج بمعرف:', docRef.id);
          return docRef.id;
        } catch (error) {
          console.error('❌ خطأ في إضافة المنتج:', error);
          throw error;
        }
      },
      // دالة لإضافة منتجات تجريبية
      initializeDefaultProducts: async function() {
        try {
          console.log('🔄 JavaScript: إضافة منتجات تجريبية...');
          const db = firebase.firestore();

          const products = [
            {
              name: 'عدسات لاصقة زرقاء',
              price: 25000,
              currency: 'IQD',
              category: 'عدسات لاصقة',
              brand: 'VisionLens',
              description: 'عدسات لاصقة ملونة زرقاء عالية الجودة',
              imageUrl: 'https://via.placeholder.com/300x300/0066cc/ffffff?text=Blue+Lens',
              inStock: true,
              quantity: 50,
              createdAt: new Date(),
              updatedAt: new Date()
            },
            {
              name: 'عدسات خضراء',
              price: 30000,
              currency: 'IQD',
              category: 'عدسات لاصقة',
              brand: 'VisionLens',
              description: 'عدسات لاصقة ملونة خضراء طبيعية',
              imageUrl: 'https://via.placeholder.com/300x300/00cc66/ffffff?text=Green+Lens',
              inStock: true,
              quantity: 30,
              createdAt: new Date(),
              updatedAt: new Date()
            },
            {
              name: 'عدسات ملونة',
              price: 35000,
              currency: 'IQD',
              category: 'عدسات لاصقة',
              brand: 'VisionLens',
              description: 'مجموعة متنوعة من العدسات الملونة',
              imageUrl: 'https://via.placeholder.com/300x300/cc6600/ffffff?text=Colored+Lens',
              inStock: true,
              quantity: 25,
              createdAt: new Date(),
              updatedAt: new Date()
            }
          ];

          for (const product of products) {
            await db.collection('products').add(product);
            console.log('✅ JavaScript: تم إضافة منتج:', product.name);
          }

          console.log('✅ JavaScript: تم إضافة جميع المنتجات التجريبية');
          return true;
        } catch (error) {
          console.error('❌ JavaScript: خطأ في إضافة المنتجات:', error);
          return false;
        }
      },
      // دالة لإنشاء طلبات تجريبية
      createSampleOrders: async function() {
        try {
          console.log('🔄 JavaScript: إنشاء طلبات تجريبية...');
          const db = firebase.firestore();

          const orders = [
            {
              id: Date.now().toString(),
              userId: '1749394548616698',
              userEmail: '<EMAIL>',
              items: [
                {
                  productId: 'prod1',
                  name: 'عدسات لاصقة زرقاء',
                  price: 25000,
                  quantity: 2
                }
              ],
              total: 50000,
              currency: 'IQD',
              status: 'pending',
              createdAt: new Date(),
              updatedAt: new Date()
            },
            {
              id: 'ORD' + Date.now().toString(),
              userId: '1749394548616698',
              userEmail: '<EMAIL>',
              items: [
                {
                  productId: 'prod2',
                  name: 'عدسات خضراء',
                  price: 30000,
                  quantity: 1
                }
              ],
              total: 30000,
              currency: 'IQD',
              status: 'confirmed',
              createdAt: new Date(),
              updatedAt: new Date()
            }
          ];

          for (const order of orders) {
            await db.collection('orders').add(order);
            console.log('✅ JavaScript: تم إنشاء طلب:', order.id);
          }

          console.log('✅ JavaScript: تم إنشاء جميع الطلبات التجريبية');
          return true;
        } catch (error) {
          console.error('❌ JavaScript: خطأ في إنشاء الطلبات:', error);
          return false;
        }
      },
      // دالة لجلب المنتجات (async)
      getProductsAsync: async function() {
        try {
          console.log('🔄 JavaScript: جلب المنتجات...');
          const db = firebase.firestore();
          const snapshot = await db.collection('products').get();
          const products = [];
          snapshot.forEach(doc => {
            products.push({ id: doc.id, ...doc.data() });
          });
          console.log('✅ JavaScript: تم جلب', products.length, 'منتج');
          return products;
        } catch (error) {
          console.error('❌ JavaScript: خطأ في جلب المنتجات:', error);
          return [];
        }
      },
      // دالة لجلب الفئات المخزنة مؤقتاً
      getCachedCategories: async function() {
        try {
          console.log('🔄 JavaScript: جلب الفئات...');
          const db = firebase.firestore();
          const snapshot = await db.collection('categories').get();
          const categories = [];
          snapshot.forEach(doc => {
            categories.push({ id: doc.id, ...doc.data() });
          });
          console.log('✅ JavaScript: تم جلب', categories.length, 'فئة');
          return categories;
        } catch (error) {
          console.error('❌ JavaScript: خطأ في جلب الفئات:', error);
          return [];
        }
      },
      // دالة لجلب الطلبات المخزنة مؤقتاً
      getCachedOrders: async function() {
        try {
          console.log('🔄 JavaScript: جلب الطلبات...');
          const db = firebase.firestore();
          const snapshot = await db.collection('orders').get();
          const orders = [];
          snapshot.forEach(doc => {
            orders.push({ id: doc.id, ...doc.data() });
          });
          console.log('✅ JavaScript: تم جلب', orders.length, 'طلب');
          return orders;
        } catch (error) {
          console.error('❌ JavaScript: خطأ في جلب الطلبات:', error);
          return [];
        }
      },
      // دالة لجلب المستخدمين
      getUsers: async function() {
        try {
          console.log('🔄 JavaScript: جلب المستخدمين...');
          const db = firebase.firestore();
          const snapshot = await db.collection('users').get();
          const users = [];
          snapshot.forEach(doc => {
            users.push({ id: doc.id, ...doc.data() });
          });
          console.log('✅ JavaScript: تم جلب', users.length, 'مستخدم');
          return users;
        } catch (error) {
          console.error('❌ JavaScript: خطأ في جلب المستخدمين:', error);
          return [];
        }
      },
      // دالة لجلب الطلبات (async)
      getOrdersAsync: async function() {
        try {
          console.log('🔄 JavaScript: جلب الطلبات (async)...');
          const db = firebase.firestore();
          const snapshot = await db.collection('orders').get();
          const orders = [];
          snapshot.forEach(doc => {
            orders.push({ id: doc.id, ...doc.data() });
          });
          console.log('✅ JavaScript: تم جلب', orders.length, 'طلب (async)');
          return orders;
        } catch (error) {
          console.error('❌ JavaScript: خطأ في جلب الطلبات (async):', error);
          return [];
        }
      }
    };

    console.log('🔥 Firebase initialized successfully');
    console.log('🛠️ firebaseHelpers متاح الآن');
  </script>

  <script>
    // Hide loading screen when Flutter is ready
    window.addEventListener('flutter-first-frame', function() {
      const loading = document.getElementById('loading');
      if (loading) {
        loading.style.display = 'none';
      }
    });
  </script>

  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
