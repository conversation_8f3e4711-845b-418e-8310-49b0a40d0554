<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="VisionLens - متجر النظارات والعدسات اللاصقة">

  <!-- Google Sign-In Configuration -->
  <!-- ⚠️ استبدل CLIENT_ID بالقيمة الحقيقية من Google Cloud Console -->
  <meta name="google-signin-client_id" content="627749384715-lj6fcgr8pku7ajphb1859qsq894mlplb.apps.googleusercontent.com">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="VisionLens">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>VisionLens</title>
  <link rel="manifest" href="manifest.json">

  <style>
    body {
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
      font-family: 'Roboto', sans-serif;
    }

    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      flex-direction: column;
    }

    .loading-spinner {
      width: 50px;
      height: 50px;
      border: 4px solid #e3f2fd;
      border-top: 4px solid #2196f3;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .loading-text {
      margin-top: 20px;
      color: #2196f3;
      font-size: 16px;
    }
  </style>
</head>
<body>
  <div id="loading" class="loading">
    <div class="loading-spinner"></div>
    <div class="loading-text">جاري تحميل VisionLens...</div>
  </div>

  <!-- Firebase SDK -->
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-auth-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-firestore-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-storage-compat.js"></script>

  <!-- Google Sign-In -->
  <script src="https://accounts.google.com/gsi/client" async defer></script>

  <!-- File Picker Support -->
  <script>
    // تحسين دعم File Picker للويب
    window.addEventListener('DOMContentLoaded', function() {
      // إضافة دعم لـ file input
      if (!window.FileReader) {
        console.warn('FileReader API غير مدعوم في هذا المتصفح');
      }

      // تهيئة File Picker للويب
      if (!window.FilePicker) {
        window.FilePicker = {};
      }

      // دالة بديلة لاختيار الملفات - محسنة للنشر المباشر
      window.pickImageFile = function() {
        console.log('🔄 بدء اختيار الملف عبر HTML Input...');
        return new Promise((resolve, reject) => {
          try {
            // إنشاء input element
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/png,image/jpeg,image/jpg';
            input.style.position = 'absolute';
            input.style.left = '-9999px';
            input.style.opacity = '0';

            // معالج تغيير الملف
            input.onchange = function(event) {
              console.log('📁 تم اختيار ملف');
              const file = event.target.files[0];
              if (file) {
                console.log('📄 تفاصيل الملف:', file.name, file.size, file.type);

                // التحقق من نوع الملف
                if (!file.type.startsWith('image/')) {
                  reject(new Error('يرجى اختيار ملف صورة صالح'));
                  cleanup();
                  return;
                }

                // التحقق من حجم الملف (2MB)
                if (file.size > 2 * 1024 * 1024) {
                  reject(new Error('حجم الملف كبير جداً. الحد الأقصى 2MB'));
                  cleanup();
                  return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                  console.log('✅ تم قراءة الملف بنجاح');
                  resolve({
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    data: e.target.result
                  });
                  cleanup();
                };
                reader.onerror = function() {
                  console.error('❌ فشل في قراءة الملف');
                  reject(new Error('فشل في قراءة الملف'));
                  cleanup();
                };
                reader.readAsDataURL(file);
              } else {
                console.log('❌ لم يتم اختيار أي ملف');
                resolve(null);
                cleanup();
              }
            };

            // معالج الإلغاء
            input.oncancel = function() {
              console.log('🚫 تم إلغاء اختيار الملف');
              resolve(null);
              cleanup();
            };

            // دالة التنظيف
            function cleanup() {
              try {
                if (input && input.parentNode) {
                  input.parentNode.removeChild(input);
                }
              } catch (e) {
                console.warn('تحذير في التنظيف:', e);
              }
            }

            // إضافة العنصر وتفعيله
            document.body.appendChild(input);

            // تأخير قصير قبل النقر
            setTimeout(() => {
              input.click();
            }, 100);

          } catch (error) {
            console.error('❌ خطأ في إنشاء file input:', error);
            reject(error);
          }
        });
      };

      // تحسين أداء File Picker
      document.addEventListener('change', function(e) {
        if (e.target && e.target.type === 'file') {
          console.log('تم اختيار ملف:', e.target.files[0]?.name);
        }
      });

      // إضافة دعم للـ MIME types
      window.addEventListener('load', function() {
        console.log('File Picker initialized for web');
      });
    });
  </script>

  <!-- Firebase Configuration -->
  <script>
    // Firebase configuration
    const firebaseConfig = {
      apiKey: "AIzaSyDkc8p_soWCQzm91z2zsYoPUyDpk21WdFw",
      authDomain: "visionlens-app-5ab70.firebaseapp.com",
      projectId: "visionlens-app-5ab70",
      storageBucket: "visionlens-app-5ab70.appspot.com",
      messagingSenderId: "580889516677",
      appId: "1:580889516677:web:a7cb9386ef6d04c7fe67ec"
    };

    // Initialize Firebase
    firebase.initializeApp(firebaseConfig);
    const db = firebase.firestore();

    console.log('🔥 Firebase initialized successfully');

    // دوال Firebase للاستخدام من Flutter
    window.firebaseHelpers = {
      // متغير لحفظ المنتجات والفئات والمستخدمين والطلبات والبراندات
      cachedProducts: [],
      cachedCategories: [],
      cachedUsers: [],
      cachedOrders: [],
      cachedBrands: [],
      isLoading: false,

      // مسح جميع البيانات المحفوظة
      clearCache() {
        console.log('🗑️ JavaScript: مسح جميع البيانات المحفوظة...');
        this.cachedProducts = [];
        this.cachedCategories = [];
        this.cachedUsers = [];
        this.cachedOrders = [];
        this.cachedBrands = [];
        this.isLoading = false;
        console.log('✅ JavaScript: تم مسح جميع البيانات المحفوظة');
      },

      // جلب المنتجات مع Promise صحيح
      async getProductsAsync() {
        if (this.isLoading) {
          console.log('⏳ JavaScript: جلب المنتجات قيد التنفيذ...');
          return this.cachedProducts;
        }

        this.isLoading = true;
        try {
          console.log('🔄 JavaScript: جلب المنتجات من Firestore...');
          const snapshot = await db.collection('products').get();
          const products = [];

          // معالجة كل منتج
          for (const doc of snapshot.docs) {
            const productData = { id: doc.id, ...doc.data() };

            // التحقق من وجود صور إضافية
            if (productData.hasMultipleImages && productData.totalImages > 1) {
              console.log(`🔄 JavaScript: جلب الصور الإضافية للمنتج: ${productData.name}`);

              try {
                // جلب الصور الإضافية من المجموعة الفرعية
                const imagesSnapshot = await db.collection('products')
                  .doc(doc.id)
                  .collection('images')
                  .orderBy('index')
                  .get();

                const additionalImages = [];
                imagesSnapshot.forEach(imageDoc => {
                  const imageData = imageDoc.data();
                  additionalImages.push(imageData.url);
                });

                // دمج الصور: الصورة الرئيسية + الصور الإضافية
                if (additionalImages.length > 0) {
                  productData.images = [
                    ...(productData.images || []),
                    ...additionalImages
                  ];
                  console.log(`✅ JavaScript: تم جلب ${additionalImages.length} صورة إضافية للمنتج: ${productData.name}`);
                }
              } catch (imageError) {
                console.error(`❌ JavaScript: خطأ في جلب الصور الإضافية للمنتج ${productData.name}:`, imageError);
                // الاستمرار بالصورة الرئيسية فقط
              }
            }

            products.push(productData);
          }

          console.log(`📦 JavaScript: تم جلب ${products.length} منتج`);

          // حفظ المنتجات في المتغير العام
          this.cachedProducts = products;
          window.latestProducts = products;

          console.log(`💾 JavaScript: تم حفظ ${this.cachedProducts.length} منتج في Cache`);

          return products;
        } catch (error) {
          console.error('❌ JavaScript: خطأ في جلب المنتجات:', error);
          this.cachedProducts = [];
          return [];
        } finally {
          this.isLoading = false;
        }
      },

      // جلب المنتجات (النسخة القديمة للتوافق)
      async getProducts() {
        return await this.getProductsAsync();
      },

      // جلب المنتجات المحفوظة
      getCachedProducts() {
        console.log(`📦 JavaScript: إرجاع ${this.cachedProducts.length} منتج محفوظ`);
        return this.cachedProducts;
      },

      // إضافة منتج مع Promise صحيح
      addProduct(productData) {
        console.log('🔄 JavaScript: بدء إضافة منتج:', productData.name);

        return new Promise(async (resolve, reject) => {
          try {
            console.log('🔗 JavaScript: مسار المنتج:', `products/${productData.id}`);

            // تحقق من حجم البيانات
            const dataSize = JSON.stringify(productData).length;
            console.log('📊 JavaScript: حجم بيانات المنتج:', dataSize, 'حرف');

            if (dataSize > 200000) { // 200KB
              console.log('⚠️ JavaScript: البيانات كبيرة، سيتم تقسيمها...');

              // فصل الصور عن البيانات الأساسية
              const { images, ...basicData } = productData;

              // حفظ البيانات الأساسية أولاً
              console.log('🔄 JavaScript: حفظ البيانات الأساسية...');
              await db.collection('products').doc(productData.id).set({
                ...basicData,
                images: images ? [images[0]] : [], // الصورة الرئيسية فقط
                hasMultipleImages: images && images.length > 1,
                totalImages: images ? images.length : 0
              });

              // حفظ الصور الإضافية في مجموعة فرعية إذا وجدت
              if (images && images.length > 1) {
                console.log('🔄 JavaScript: حفظ الصور الإضافية...');
                const additionalImages = images.slice(1);

                for (let i = 0; i < additionalImages.length; i++) {
                  await db.collection('products')
                    .doc(productData.id)
                    .collection('images')
                    .doc(`image_${i + 1}`)
                    .set({
                      url: additionalImages[i],
                      index: i + 1,
                      createdAt: new Date().toISOString()
                    });
                }
                console.log(`✅ JavaScript: تم حفظ ${additionalImages.length} صورة إضافية`);
              }
            } else {
              // البيانات صغيرة، حفظ عادي
              console.log('📝 JavaScript: بيانات المنتج (حجم صغير):', {
                name: productData.name,
                imagesCount: productData.images ? productData.images.length : 0
              });

              await db.collection('products').doc(productData.id).set(productData);
            }

            console.log('✅ JavaScript: تم إضافة المنتج بنجاح في Firebase:', productData.name);

            // تحديث Cache
            await this.getProductsAsync();
            console.log('🔄 JavaScript: تم تحديث Cache بعد الإضافة');

            resolve({ success: true });
          } catch (error) {
            console.error('❌ JavaScript: خطأ في إضافة المنتج:', error);
            console.error('❌ JavaScript: تفاصيل الخطأ:', error.code, error.message);

            // معلومات إضافية للتشخيص
            if (error.code === 'invalid-argument') {
              console.error('❌ JavaScript: خطأ في صيغة البيانات - ربما البيانات كبيرة جداً');
            }

            reject({ success: false, error: error.message });
          }
        });
      },

      // تعديل منتج مع Promise صحيح
      updateProduct(productData) {
        console.log('🔄 JavaScript: بدء تعديل منتج:', productData.name);

        return new Promise(async (resolve, reject) => {
          try {
            console.log('🔗 JavaScript: مسار المنتج:', `products/${productData.id}`);
            console.log('📝 JavaScript: بيانات التعديل:', productData);

            // التحقق من وجود المنتج أولاً
            const docRef = db.collection('products').doc(productData.id);
            const docSnap = await docRef.get();

            if (!docSnap.exists) {
              console.log('⚠️ JavaScript: المنتج غير موجود:', productData.id);
              resolve({ success: false, error: 'المنتج غير موجود' });
              return;
            }

            console.log('📋 JavaScript: المنتج موجود، بدء التعديل...');
            const result = await docRef.update(productData);
            console.log('✅ JavaScript: تم تعديل المنتج بنجاح في Firebase:', productData.name);
            console.log('📊 JavaScript: نتيجة التعديل:', result);

            // تحديث Cache
            await this.getProductsAsync();
            console.log('🔄 JavaScript: تم تحديث Cache بعد التعديل');

            resolve({ success: true });
          } catch (error) {
            console.error('❌ JavaScript: خطأ في تعديل المنتج:', error);
            console.error('❌ JavaScript: تفاصيل الخطأ:', error.code, error.message);
            reject({ success: false, error: error.message });
          }
        });
      },

      // حذف منتج مع Promise صحيح
      deleteProduct(productId) {
        console.log('🔄 JavaScript: بدء حذف منتج:', productId);

        return new Promise(async (resolve, reject) => {
          try {
            console.log('🔗 JavaScript: مسار المنتج:', `products/${productId}`);

            // التحقق من وجود المنتج أولاً
            const docRef = db.collection('products').doc(productId);
            const docSnap = await docRef.get();

            if (!docSnap.exists) {
              console.log('⚠️ JavaScript: المنتج غير موجود:', productId);
              resolve({ success: false, error: 'المنتج غير موجود' });
              return;
            }

            console.log('📋 JavaScript: المنتج موجود، بدء الحذف...');
            await docRef.delete();
            console.log('✅ JavaScript: تم حذف المنتج بنجاح من Firebase:', productId);

            // تحديث Cache
            await this.getProductsAsync();
            console.log('🔄 JavaScript: تم تحديث Cache بعد الحذف');

            // التحقق من الحذف
            const checkSnap = await docRef.get();
            if (!checkSnap.exists) {
              console.log('✅ JavaScript: تأكيد الحذف - المنتج لم يعد موجود');
              resolve({ success: true });
            } else {
              console.log('❌ JavaScript: فشل الحذف - المنتج ما زال موجود');
              resolve({ success: false, error: 'فشل في الحذف' });
            }

          } catch (error) {
            console.error('❌ JavaScript: خطأ في حذف المنتج:', error);
            console.error('❌ JavaScript: تفاصيل الخطأ:', error.code, error.message);
            reject({ success: false, error: error.message });
          }
        });
      },

      // جلب الفئات مع Promise صحيح
      getCategories() {
        console.log('🔄 JavaScript: جلب الفئات من Firestore...');

        return new Promise(async (resolve, reject) => {
          try {
            const snapshot = await db.collection('categories').get();
            const categories = [];
            snapshot.forEach(doc => {
              categories.push({ id: doc.id, ...doc.data() });
            });
            console.log(`📦 JavaScript: تم جلب ${categories.length} فئة`);

            // حفظ في Cache
            this.cachedCategories = categories;
            console.log(`💾 JavaScript: تم حفظ ${categories.length} فئة في Cache`);

            resolve(categories);
          } catch (error) {
            console.error('❌ JavaScript: خطأ في جلب الفئات:', error);
            reject(error);
          }
        });
      },

      // جلب الفئات من Cache
      getCachedCategories() {
        console.log(`📦 JavaScript: إرجاع ${this.cachedCategories ? this.cachedCategories.length : 0} فئة من Cache`);
        return this.cachedCategories || [];
      },

      // إضافة فئة جديدة مع Promise صحيح
      addCategory(categoryData) {
        console.log('🔄 JavaScript: بدء إضافة فئة:', categoryData.name);

        return new Promise(async (resolve, reject) => {
          try {
            console.log('🔗 JavaScript: مسار الفئة:', `categories/${categoryData.id}`);
            console.log('📝 JavaScript: بيانات الفئة:', categoryData);

            await db.collection('categories').doc(categoryData.id).set(categoryData);
            console.log('✅ JavaScript: تم إضافة الفئة بنجاح في Firebase:', categoryData.name);

            // تحديث Cache
            await this.getCategories();
            console.log('🔄 JavaScript: تم تحديث Cache بعد إضافة الفئة');

            resolve({ success: true });
          } catch (error) {
            console.error('❌ JavaScript: خطأ في إضافة الفئة:', error);
            console.error('❌ JavaScript: تفاصيل الخطأ:', error.code, error.message);
            reject({ success: false, error: error.message });
          }
        });
      },

      // تعديل فئة مع Promise صحيح
      updateCategory(categoryData) {
        console.log('🔄 JavaScript: بدء تعديل فئة:', categoryData.name);

        return new Promise(async (resolve, reject) => {
          try {
            console.log('🔗 JavaScript: مسار الفئة:', `categories/${categoryData.id}`);
            console.log('📝 JavaScript: بيانات التعديل:', categoryData);

            // التحقق من وجود الفئة أولاً
            const docRef = db.collection('categories').doc(categoryData.id);
            const docSnap = await docRef.get();

            if (!docSnap.exists) {
              console.log('⚠️ JavaScript: الفئة غير موجودة:', categoryData.id);
              resolve({ success: false, error: 'الفئة غير موجودة' });
              return;
            }

            console.log('📋 JavaScript: الفئة موجودة، بدء التعديل...');
            await docRef.update(categoryData);
            console.log('✅ JavaScript: تم تعديل الفئة بنجاح في Firebase:', categoryData.name);

            // تحديث Cache
            await this.getCategories();
            console.log('🔄 JavaScript: تم تحديث Cache بعد تعديل الفئة');

            resolve({ success: true });
          } catch (error) {
            console.error('❌ JavaScript: خطأ في تعديل الفئة:', error);
            console.error('❌ JavaScript: تفاصيل الخطأ:', error.code, error.message);
            reject({ success: false, error: error.message });
          }
        });
      },

      // حذف فئة مع Promise صحيح
      deleteCategory(categoryId) {
        console.log('🔄 JavaScript: بدء حذف فئة:', categoryId);

        return new Promise(async (resolve, reject) => {
          try {
            console.log('🔗 JavaScript: مسار الفئة:', `categories/${categoryId}`);

            // التحقق من وجود الفئة أولاً
            const docRef = db.collection('categories').doc(categoryId);
            const docSnap = await docRef.get();

            if (!docSnap.exists) {
              console.log('⚠️ JavaScript: الفئة غير موجودة:', categoryId);
              resolve({ success: false, error: 'الفئة غير موجودة' });
              return;
            }

            console.log('📋 JavaScript: الفئة موجودة، بدء الحذف...');
            await docRef.delete();
            console.log('✅ JavaScript: تم حذف الفئة بنجاح من Firebase:', categoryId);

            // تحديث Cache
            await this.getCategories();
            console.log('🔄 JavaScript: تم تحديث Cache بعد حذف الفئة');

            // التحقق من الحذف
            const checkSnap = await docRef.get();
            if (!checkSnap.exists) {
              console.log('✅ JavaScript: تأكيد الحذف - الفئة لم تعد موجودة');
              resolve({ success: true });
            } else {
              console.log('❌ JavaScript: فشل الحذف - الفئة ما زالت موجودة');
              resolve({ success: false, error: 'فشل في الحذف' });
            }

          } catch (error) {
            console.error('❌ JavaScript: خطأ في حذف الفئة:', error);
            console.error('❌ JavaScript: تفاصيل الخطأ:', error.code, error.message);
            reject({ success: false, error: error.message });
          }
        });
      },

      // جلب المستخدمين من Cache
      getCachedUsers() {
        console.log(`👥 JavaScript: إرجاع ${this.cachedUsers ? this.cachedUsers.length : 0} مستخدم من Cache`);
        return this.cachedUsers || [];
      },

      // جلب الطلبات من Cache
      getCachedOrders() {
        console.log(`📦 JavaScript: إرجاع ${this.cachedOrders ? this.cachedOrders.length : 0} طلب من Cache`);
        return this.cachedOrders || [];
      },

      // تحديث حالة الطلب مع Promise صحيح
      async updateOrderStatus(orderId, status) {
        try {
          console.log('🔄 JavaScript: تحديث حالة الطلب:', orderId, 'إلى:', status);

          // تنظيف معرف الطلب
          const cleanOrderId = orderId.toString().trim();
          console.log('🔍 JavaScript: معرف الطلب المنظف:', cleanOrderId);

          const firestore = firebase.firestore();
          const docRef = firestore.collection('orders').doc(cleanOrderId);

          // التحقق من وجود المستند أولاً
          try {
            console.log('🔍 JavaScript: التحقق من وجود المستند...');
            const docSnapshot = await docRef.get();
            if (docSnapshot.exists) {
              const currentData = docSnapshot.data();
              console.log('🔄 JavaScript: بيانات المستند الحالية:', JSON.stringify(currentData, null, 2));
              console.log('🔄 JavaScript: الحالة الحالية:', currentData.status);
            } else {
              console.error('❌ JavaScript: المستند غير موجود في Firestore!');
              console.error('❌ JavaScript: معرف الطلب المستخدم:', cleanOrderId);

              // محاولة البحث عن الطلب بطرق أخرى
              console.log('🔍 JavaScript: البحث عن الطلب في جميع المستندات...');
              const allOrders = await firestore.collection('orders').get();
              console.log('🔍 JavaScript: عدد الطلبات الموجودة:', allOrders.size);

              let foundDoc = null;
              allOrders.forEach(doc => {
                console.log('🔍 JavaScript: طلب موجود:', doc.id);
                if (doc.id === cleanOrderId || doc.id === orderId) {
                  foundDoc = doc;
                  console.log('✅ JavaScript: تم العثور على الطلب:', doc.id);
                }
              });

              if (!foundDoc) {
                throw new Error('المستند غير موجود في Firestore');
              }
            }
          } catch (getError) {
            console.error('❌ JavaScript: خطأ في جلب المستند:', getError);
            throw getError;
          }

          console.log('🔄 JavaScript: تحديث المستند في Firestore...');
          const updateData = {
            status: status,
            updatedAt: firebase.firestore.FieldValue.serverTimestamp()
          };
          console.log('🔄 JavaScript: بيانات التحديث:', updateData);

          const updateResult = await docRef.update(updateData);
          console.log('🔄 JavaScript: نتيجة التحديث:', updateResult);
          console.log('✅ JavaScript: تم تحديث حالة الطلب بنجاح في Firebase');

          // التحقق من التحديث
          console.log('🔄 JavaScript: التحقق من التحديث...');
          const updatedDoc = await docRef.get();
          if (updatedDoc.exists) {
            const updatedData = updatedDoc.data();
            console.log('✅ JavaScript: البيانات بعد التحديث:', JSON.stringify(updatedData, null, 2));
            console.log('✅ JavaScript: الحالة الجديدة:', updatedData.status);
          }

          // مسح Cache وإعادة جلب البيانات
          console.log('🔄 JavaScript: مسح Cache...');
          this.cachedOrders = [];
          this.isLoading = false;

          console.log('🔄 JavaScript: إعادة جلب البيانات...');
          await this.getOrdersAsync();

          console.log('✅ JavaScript: تم تحديث Cache بعد تحديث الحالة');
          return { success: true };
        } catch (error) {
          console.error('❌ JavaScript: خطأ في تحديث حالة الطلب:', error);
          console.error('❌ JavaScript: نوع الخطأ:', typeof error);
          console.error('❌ JavaScript: رسالة الخطأ:', error.message);
          console.error('❌ JavaScript: كود الخطأ:', error.code);
          console.error('❌ JavaScript: تفاصيل الخطأ الكاملة:', JSON.stringify(error, null, 2));
          return { success: false, error: error.message };
        }
      },

      // جلب المستخدمين مع Promise صحيح
      getUsers() {
        console.log('🔄 JavaScript: جلب المستخدمين من Firestore...');

        return new Promise(async (resolve, reject) => {
          try {
            const snapshot = await db.collection('users').get();
            const users = [];
            snapshot.forEach(doc => {
              users.push({ id: doc.id, ...doc.data() });
            });
            console.log(`👥 JavaScript: تم جلب ${users.length} مستخدم`);

            // حفظ في Cache
            this.cachedUsers = users;
            console.log(`💾 JavaScript: تم حفظ ${users.length} مستخدم في Cache`);

            resolve(users);
          } catch (error) {
            console.error('❌ JavaScript: خطأ في جلب المستخدمين:', error);
            reject(error);
          }
        });
      },

      // جلب الطلبات مع Promise صحيح (نسخة محسنة)
      async getOrdersAsync() {
        try {
          console.log('🔄 JavaScript: جلب الطلبات من Firestore...');

          const snapshot = await db.collection('orders').get();
          const orders = [];

          console.log(`📊 JavaScript: عدد المستندات الموجودة: ${snapshot.size}`);

          snapshot.forEach(doc => {
            const data = doc.data();
            console.log(`🔍 JavaScript: معالجة طلب ${doc.id}:`, data);

            orders.push({
              id: doc.id,
              ...data
            });
          });

          console.log(`📦 JavaScript: تم جلب ${orders.length} طلب من Firestore`);

          // طباعة تفاصيل أول 3 طلبات للتحقق
          for (let i = 0; i < Math.min(orders.length, 3); i++) {
            console.log(`📋 JavaScript: طلب ${i + 1}:`, {
              id: orders[i].id,
              customerName: orders[i].customerName,
              status: orders[i].status,
              total: orders[i].total
            });
          }

          // حفظ في Cache
          this.cachedOrders = orders;
          console.log(`💾 JavaScript: تم حفظ ${orders.length} طلب في Cache`);

          return orders;
        } catch (error) {
          console.error('❌ JavaScript: خطأ في جلب الطلبات:', error);
          return this.cachedOrders || [];
        }
      },

      // جلب الطلبات (النسخة القديمة للتوافق)
      async getOrders() {
        return await this.getOrdersAsync();
      },

      // إضافة طلب جديد باستخدام معرف واضح
      async addOrder(orderData) {
        const maxRetries = 3;
        let lastError;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
          try {
            console.log(`🔄 JavaScript: محاولة ${attempt}/${maxRetries} لإضافة طلب جديد`);
            console.log('🔄 JavaScript: معرف الطلب الواضح:', orderData.id);
            console.log('🔄 JavaScript: بيانات الطلب:', JSON.stringify(orderData));

            if (!orderData || !orderData.id) {
              throw new Error('بيانات الطلب غير صحيحة');
            }

            // انتظار قصير قبل المحاولة
            if (attempt > 1) {
              console.log(`⏳ JavaScript: انتظار ${attempt * 1000}ms قبل المحاولة...`);
              await new Promise(resolve => setTimeout(resolve, attempt * 1000));
            }

            console.log('🔄 JavaScript: إنشاء مرجع المجموعة...');
            const ordersCollection = db.collection('orders');
            console.log('🔄 JavaScript: تم إنشاء مرجع المجموعة');

            // استخدام معرف الطلب الواضح كمعرف المستند
            console.log('🔄 JavaScript: بدء إضافة المستند بمعرف واضح...');
            await ordersCollection.doc(orderData.id).set(orderData);
            console.log('🔄 JavaScript: تم إضافة المستند بنجاح');

            console.log('✅ JavaScript: تم إضافة الطلب بنجاح');
            console.log('✅ JavaScript: معرف المستند:', orderData.id);
            console.log('✅ JavaScript: معرف الطلب:', orderData.id);

            // مسح Cache لإجبار إعادة جلب البيانات
            this.cachedOrders = [];
            console.log('🗑️ JavaScript: تم مسح Cache الطلبات');

            return orderData.id; // إرجاع نفس المعرف الواضح
          } catch (error) {
            lastError = error;
            console.error(`❌ JavaScript: فشلت المحاولة ${attempt}/${maxRetries}:`, error.message);

            if (attempt === maxRetries) {
              console.error('❌ JavaScript: فشلت جميع المحاولات');
              console.error('❌ JavaScript: آخر خطأ:', error);
              throw error;
            }
          }
        }
      },

      // تحديث حالة الطلب باستخدام المعرف الواضح
      async updateOrderStatus(orderId, newStatus) {
        try {
          console.log('🔄 JavaScript: تحديث حالة الطلب بالمعرف الواضح:', orderId, 'إلى:', newStatus);

          if (!orderId || !newStatus) {
            throw new Error('معرف الطلب أو الحالة الجديدة فارغ');
          }

          // استخدام المعرف الواضح مباشرة
          const orderRef = db.collection('orders').doc(orderId);
          await orderRef.update({
            status: newStatus,
            updatedAt: new Date().toISOString()
          });

          console.log('✅ JavaScript: تم تحديث حالة الطلب بنجاح للمعرف:', orderId);

          // مسح Cache لإجبار إعادة جلب البيانات
          this.cachedOrders = [];
          console.log('🗑️ JavaScript: تم مسح Cache الطلبات');

          return true;
        } catch (error) {
          console.error('❌ JavaScript: خطأ في تحديث حالة الطلب:', orderId, error);
          throw error;
        }
      },

      // === إدارة العلامات التجارية ===

      // جلب البراندات من Firestore
      async getBrands() {
        try {
          console.log('🔄 JavaScript: جلب البراندات من Firestore...');
          const snapshot = await db.collection('brands').get();

          const brands = [];
          snapshot.forEach(doc => {
            const data = doc.data();
            brands.push({
              id: doc.id,
              ...data
            });
          });

          console.log(`📦 JavaScript: تم جلب ${brands.length} براند`);
          console.log('💾 JavaScript: تم حفظ البراندات في Cache');

          // حفظ في Cache
          this.cachedBrands = brands;

          return brands;
        } catch (error) {
          console.error('❌ JavaScript: خطأ في جلب البراندات:', error);
          return [];
        }
      },

      // جلب البراندات من Cache
      getCachedBrands() {
        console.log(`🏷️ JavaScript: إرجاع ${this.cachedBrands ? this.cachedBrands.length : 0} براند من Cache`);
        return this.cachedBrands || [];
      },

      // إضافة براند جديد مع Promise صحيح
      addBrand(brandData) {
        console.log('🔄 JavaScript: بدء إضافة براند:', brandData.name);

        return new Promise(async (resolve, reject) => {
          try {
            console.log('🔗 JavaScript: مسار البراند:', `brands/${brandData.id}`);
            console.log('📝 JavaScript: بيانات البراند:', brandData);

            await db.collection('brands').doc(brandData.id).set(brandData);
            console.log('✅ JavaScript: تم إضافة البراند بنجاح في Firebase:', brandData.name);

            // تحديث Cache
            await this.getBrands();
            console.log('🔄 JavaScript: تم تحديث Cache بعد إضافة البراند');

            resolve({ success: true });
          } catch (error) {
            console.error('❌ JavaScript: خطأ في إضافة البراند:', error);
            console.error('❌ JavaScript: تفاصيل الخطأ:', error.code, error.message);
            reject({ success: false, error: error.message });
          }
        });
      },

      // تعديل براند مع Promise صحيح
      updateBrand(brandData) {
        console.log('🔄 JavaScript: بدء تعديل براند:', brandData.name);

        return new Promise(async (resolve, reject) => {
          try {
            console.log('🔗 JavaScript: مسار البراند:', `brands/${brandData.id}`);
            console.log('📝 JavaScript: بيانات التعديل:', brandData);

            // التحقق من وجود البراند أولاً
            const docRef = db.collection('brands').doc(brandData.id);
            const docSnap = await docRef.get();

            if (!docSnap.exists) {
              console.log('⚠️ JavaScript: البراند غير موجود:', brandData.id);
              resolve({ success: false, error: 'البراند غير موجود' });
              return;
            }

            console.log('📋 JavaScript: البراند موجود، بدء التعديل...');
            await docRef.update(brandData);
            console.log('✅ JavaScript: تم تعديل البراند بنجاح في Firebase:', brandData.name);

            // تحديث Cache
            await this.getBrands();
            console.log('🔄 JavaScript: تم تحديث Cache بعد تعديل البراند');

            resolve({ success: true });
          } catch (error) {
            console.error('❌ JavaScript: خطأ في تعديل البراند:', error);
            console.error('❌ JavaScript: تفاصيل الخطأ:', error.code, error.message);
            reject({ success: false, error: error.message });
          }
        });
      },

      // حذف براند مع Promise صحيح
      deleteBrand(brandId) {
        console.log('🔄 JavaScript: بدء حذف براند:', brandId);

        return new Promise(async (resolve, reject) => {
          try {
            console.log('🔗 JavaScript: مسار البراند:', `brands/${brandId}`);

            // التحقق من وجود البراند أولاً
            const docRef = db.collection('brands').doc(brandId);
            const docSnap = await docRef.get();

            if (!docSnap.exists) {
              console.log('⚠️ JavaScript: البراند غير موجود:', brandId);
              resolve({ success: false, error: 'البراند غير موجود' });
              return;
            }

            console.log('📋 JavaScript: البراند موجود، بدء الحذف...');
            await docRef.delete();
            console.log('✅ JavaScript: تم حذف البراند بنجاح من Firebase:', brandId);

            // تحديث Cache
            await this.getBrands();
            console.log('🔄 JavaScript: تم تحديث Cache بعد حذف البراند');

            // التحقق من الحذف
            const checkSnap = await docRef.get();
            if (!checkSnap.exists) {
              console.log('✅ JavaScript: تأكيد الحذف - البراند لم يعد موجود');
              resolve({ success: true });
            } else {
              console.log('❌ JavaScript: فشل الحذف - البراند ما زال موجود');
              resolve({ success: false, error: 'فشل في الحذف' });
            }

          } catch (error) {
            console.error('❌ JavaScript: خطأ في حذف البراند:', error);
            console.error('❌ JavaScript: تفاصيل الخطأ:', error.code, error.message);
            reject({ success: false, error: error.message });
          }
        });
      }
    };

    console.log('🔥 Firebase initialized successfully');
    console.log('🛠️ firebaseHelpers متاح الآن');

    // طباعة الدوال المتاحة للتحقق
    console.log('🔍 JavaScript: الدوال المتاحة في firebaseHelpers:');
    console.log('🔍 JavaScript: addOrder متاح:', typeof window.firebaseHelpers.addOrder);
    console.log('🔍 JavaScript: updateOrderStatus متاح:', typeof window.firebaseHelpers.updateOrderStatus);
    console.log('🔍 JavaScript: getOrdersAsync متاح:', typeof window.firebaseHelpers.getOrdersAsync);

    // === إضافة العلامات التجارية المشهورة ===
    window.addFamousBrands = async function() {
      console.log('🏷️ بدء إضافة العلامات التجارية المشهورة...');

      const famousBrands = [
        // عدسات لاصقة
        { id: 'acuvue', name: 'Acuvue', category: 'contact-lenses', description: 'عدسات لاصقة من جونسون آند جونسون', country: 'أمريكا' },
        { id: 'bausch-lomb', name: 'Bausch + Lomb', category: 'contact-lenses', description: 'عدسات لاصقة عالية الجودة', country: 'أمريكا' },
        { id: 'alcon', name: 'Alcon', category: 'contact-lenses', description: 'عدسات لاصقة طبية ومتخصصة', country: 'سويسرا' },
        { id: 'coopervision', name: 'CooperVision', category: 'contact-lenses', description: 'عدسات لاصقة مريحة وآمنة', country: 'أمريكا' },
        { id: 'freshlook', name: 'FreshLook', category: 'contact-lenses', description: 'عدسات لاصقة ملونة', country: 'أمريكا' },
        { id: 'air-optix', name: 'Air Optix', category: 'contact-lenses', description: 'عدسات لاصقة قابلة للتنفس', country: 'أمريكا' },
        { id: 'biofinity', name: 'Biofinity', category: 'contact-lenses', description: 'عدسات لاصقة سيليكون هيدروجيل', country: 'أمريكا' },
        { id: 'soflens', name: 'SofLens', category: 'contact-lenses', description: 'عدسات لاصقة ناعمة ومريحة', country: 'أمريكا' },

        // نظارات شمسية
        { id: 'ray-ban', name: 'Ray-Ban', category: 'sunglasses', description: 'نظارات شمسية أيقونية وعصرية', country: 'إيطاليا' },
        { id: 'oakley', name: 'Oakley', category: 'sunglasses', description: 'نظارات شمسية رياضية عالية الأداء', country: 'أمريكا' },
        { id: 'persol', name: 'Persol', category: 'sunglasses', description: 'نظارات شمسية إيطالية فاخرة', country: 'إيطاليا' },
        { id: 'maui-jim', name: 'Maui Jim', category: 'sunglasses', description: 'نظارات شمسية بعدسات مستقطبة', country: 'أمريكا' },
        { id: 'police', name: 'Police', category: 'sunglasses', description: 'نظارات شمسية عصرية وجريئة', country: 'إيطاليا' },
        { id: 'carrera', name: 'Carrera', category: 'sunglasses', description: 'نظارات شمسية رياضية أنيقة', country: 'النمسا' },

        // نظارات طبية
        { id: 'gucci', name: 'Gucci', category: 'eyeglasses', description: 'نظارات طبية فاخرة وأنيقة', country: 'إيطاليا' },
        { id: 'prada', name: 'Prada', category: 'eyeglasses', description: 'نظارات طبية عصرية وراقية', country: 'إيطاليا' },
        { id: 'versace', name: 'Versace', category: 'eyeglasses', description: 'نظارات طبية فاخرة ومميزة', country: 'إيطاليا' },
        { id: 'tom-ford', name: 'Tom Ford', category: 'eyeglasses', description: 'نظارات طبية أنيقة وعصرية', country: 'أمريكا' },
        { id: 'dolce-gabbana', name: 'Dolce & Gabbana', category: 'eyeglasses', description: 'نظارات طبية إيطالية فاخرة', country: 'إيطاليا' },
        { id: 'armani', name: 'Giorgio Armani', category: 'eyeglasses', description: 'نظارات طبية كلاسيكية وأنيقة', country: 'إيطاليا' },
        { id: 'calvin-klein', name: 'Calvin Klein', category: 'eyeglasses', description: 'نظارات طبية عصرية وبسيطة', country: 'أمريكا' },
        { id: 'hugo-boss', name: 'Hugo Boss', category: 'eyeglasses', description: 'نظارات طبية أنيقة ومهنية', country: 'ألمانيا' },

        // علامات عامة
        { id: 'luxottica', name: 'Luxottica', category: 'general', description: 'أكبر شركة نظارات في العالم', country: 'إيطاليا' },
        { id: 'essilor', name: 'Essilor', category: 'general', description: 'عدسات طبية عالية الجودة', country: 'فرنسا' },
        { id: 'zeiss', name: 'Carl Zeiss', category: 'general', description: 'عدسات طبية ألمانية متقدمة', country: 'ألمانيا' },
        { id: 'hoya', name: 'Hoya', category: 'general', description: 'عدسات طبية يابانية متطورة', country: 'اليابان' }
      ];

      try {
        let addedCount = 0;
        let existingCount = 0;

        for (const brand of famousBrands) {
          try {
            // التحقق من وجود البراند أولاً
            const docRef = db.collection('brands').doc(brand.id);
            const docSnap = await docRef.get();

            if (docSnap.exists) {
              console.log(`⚠️ البراند موجود بالفعل: ${brand.name}`);
              existingCount++;
              continue;
            }

            // إضافة البراند الجديد
            await docRef.set({
              ...brand,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              isActive: true
            });

            console.log(`✅ تم إضافة البراند: ${brand.name}`);
            addedCount++;

            // انتظار قصير لتجنب الضغط على Firebase
            await new Promise(resolve => setTimeout(resolve, 100));

          } catch (error) {
            console.error(`❌ خطأ في إضافة البراند ${brand.name}:`, error);
          }
        }

        console.log(`🎉 تم الانتهاء من إضافة العلامات التجارية:`);
        console.log(`✅ تم إضافة: ${addedCount} براند جديد`);
        console.log(`⚠️ موجود مسبقاً: ${existingCount} براند`);
        console.log(`📊 إجمالي العلامات: ${famousBrands.length} براند`);

        // تحديث Cache
        if (window.firebaseHelpers && window.firebaseHelpers.getBrands) {
          await window.firebaseHelpers.getBrands();
          console.log('🔄 تم تحديث Cache العلامات التجارية');
        }

        return { success: true, added: addedCount, existing: existingCount, total: famousBrands.length };

      } catch (error) {
        console.error('❌ خطأ عام في إضافة العلامات التجارية:', error);
        return { success: false, error: error.message };
      }
    };
  </script>

  <script>
    // Hide loading screen when Flutter is ready
    window.addEventListener('flutter-first-frame', function() {
      const loading = document.getElementById('loading');
      if (loading) {
        loading.style.display = 'none';
      }
    });
  </script>

  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
