<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="VisionLens - متجر النظارات والعدسات اللاصقة">

  <!-- Google Sign-In Configuration -->
  <!-- ⚠️ استبدل CLIENT_ID بالقيمة الحقيقية من Google Cloud Console -->
  <meta name="google-signin-client_id" content="627749384715-lj6fcgr8pku7ajphb1859qsq894mlplb.apps.googleusercontent.com">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="VisionLens">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>VisionLens</title>
  <link rel="manifest" href="manifest.json">

  <style>
    body {
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
      font-family: 'Roboto', sans-serif;
    }

    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      flex-direction: column;
    }

    .loading-spinner {
      width: 50px;
      height: 50px;
      border: 4px solid #e3f2fd;
      border-top: 4px solid #2196f3;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .loading-text {
      margin-top: 20px;
      color: #2196f3;
      font-size: 16px;
    }
  </style>
</head>
<body>
  <div id="loading" class="loading">
    <div class="loading-spinner"></div>
    <div class="loading-text">جاري تحميل VisionLens...</div>
  </div>

  <!-- Firebase SDK -->
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-auth-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-firestore-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-storage-compat.js"></script>

  <!-- Google Sign-In -->
  <script src="https://accounts.google.com/gsi/client" async defer></script>

  <!-- Firebase Configuration -->
  <script>
    // Firebase configuration
    const firebaseConfig = {
      apiKey: "AIzaSyDkc8p_soWCQzm91z2zsYoPUyDpk21WdFw",
      authDomain: "visionlens-app-5ab70.firebaseapp.com",
      projectId: "visionlens-app-5ab70",
      storageBucket: "visionlens-app-5ab70.appspot.com",
      messagingSenderId: "************",
      appId: "1:************:web:a7cb9386ef6d04c7fe67ec"
    };

    // Initialize Firebase
    firebase.initializeApp(firebaseConfig);
    const db = firebase.firestore();

    console.log('🔥 Firebase initialized successfully');

    // دوال Firebase للاستخدام من Flutter
    window.firebaseHelpers = {
      // متغير لحفظ المنتجات والفئات والمستخدمين والطلبات والبراندات
      cachedProducts: [],
      cachedCategories: [],
      cachedUsers: [],
      cachedOrders: [],
      cachedBrands: [],
      isLoading: false,

      // مسح جميع البيانات المحفوظة
      clearCache() {
        console.log('🗑️ JavaScript: مسح جميع البيانات المحفوظة...');
        this.cachedProducts = [];
        this.cachedCategories = [];
        this.cachedUsers = [];
        this.cachedOrders = [];
        this.cachedBrands = [];
        this.isLoading = false;
        console.log('✅ JavaScript: تم مسح جميع البيانات المحفوظة');
      },

      // جلب المنتجات مع Promise صحيح
      async getProductsAsync() {
        if (this.isLoading) {
          console.log('⏳ JavaScript: جلب المنتجات قيد التنفيذ...');
          return this.cachedProducts;
        }

        this.isLoading = true;
        try {
          console.log('🔄 JavaScript: جلب المنتجات من Firestore...');
          const snapshot = await db.collection('products').get();
          const products = [];
          snapshot.forEach(doc => {
            products.push({ id: doc.id, ...doc.data() });
          });
          console.log(`📦 JavaScript: تم جلب ${products.length} منتج`);

          // حفظ المنتجات في المتغير العام
          this.cachedProducts = products;
          window.latestProducts = products;

          console.log(`💾 JavaScript: تم حفظ ${this.cachedProducts.length} منتج في Cache`);

          return products;
        } catch (error) {
          console.error('❌ JavaScript: خطأ في جلب المنتجات:', error);
          this.cachedProducts = [];
          return [];
        } finally {
          this.isLoading = false;
        }
      },

      // جلب المنتجات (النسخة القديمة للتوافق)
      async getProducts() {
        return await this.getProductsAsync();
      },

      // جلب المنتجات المحفوظة
      getCachedProducts() {
        console.log(`📦 JavaScript: إرجاع ${this.cachedProducts.length} منتج محفوظ`);
        return this.cachedProducts;
      },

      // جلب الفئات مع Promise صحيح
      getCategories() {
        console.log('🔄 JavaScript: جلب الفئات من Firestore...');

        return new Promise(async (resolve, reject) => {
          try {
            const snapshot = await db.collection('categories').get();
            const categories = [];
            snapshot.forEach(doc => {
              categories.push({ id: doc.id, ...doc.data() });
            });
            console.log(`📦 JavaScript: تم جلب ${categories.length} فئة`);

            // حفظ في Cache
            this.cachedCategories = categories;
            console.log(`💾 JavaScript: تم حفظ ${categories.length} فئة في Cache`);

            resolve(categories);
          } catch (error) {
            console.error('❌ JavaScript: خطأ في جلب الفئات:', error);
            reject(error);
          }
        });
      },

      // جلب الفئات من Cache
      getCachedCategories() {
        console.log(`📦 JavaScript: إرجاع ${this.cachedCategories ? this.cachedCategories.length : 0} فئة من Cache`);
        return this.cachedCategories || [];
      },

      // جلب المستخدمين من Cache
      getCachedUsers() {
        console.log(`👥 JavaScript: إرجاع ${this.cachedUsers ? this.cachedUsers.length : 0} مستخدم من Cache`);
        return this.cachedUsers || [];
      },

      // جلب الطلبات من Cache
      getCachedOrders() {
        console.log(`📦 JavaScript: إرجاع ${this.cachedOrders ? this.cachedOrders.length : 0} طلب من Cache`);
        return this.cachedOrders || [];
      },

      // جلب المستخدمين مع Promise صحيح
      getUsers() {
        console.log('🔄 JavaScript: جلب المستخدمين من Firestore...');

        return new Promise(async (resolve, reject) => {
          try {
            const snapshot = await db.collection('users').get();
            const users = [];
            snapshot.forEach(doc => {
              users.push({ id: doc.id, ...doc.data() });
            });
            console.log(`👥 JavaScript: تم جلب ${users.length} مستخدم`);

            // حفظ في Cache
            this.cachedUsers = users;
            console.log(`💾 JavaScript: تم حفظ ${users.length} مستخدم في Cache`);

            resolve(users);
          } catch (error) {
            console.error('❌ JavaScript: خطأ في جلب المستخدمين:', error);
            reject(error);
          }
        });
      },

      // جلب الطلبات مع Promise صحيح (نسخة مطابقة من test)
      async getOrdersAsync() {
        if (this.isLoading) {
          console.log('⏳ JavaScript: جلب الطلبات قيد التنفيذ...');
          return this.cachedOrders;
        }

        this.isLoading = true;
        try {
          console.log('🔄 JavaScript: جلب الطلبات من Firestore...');

          const snapshot = await db.collection('orders').get();
          const orders = [];

          snapshot.forEach(doc => {
            const data = doc.data();
            console.log('🔍 JavaScript: معرف المستند:', doc.id);
            console.log('🔍 JavaScript: بيانات المستند:', data);

            if (data && Object.keys(data).length > 0) {
              const order = {
                id: doc.id,
                ...data
              };

              // معالجة التواريخ بأمان
              if (data.createdAt) {
                try {
                  if (typeof data.createdAt === 'string') {
                    order.createdAt = data.createdAt;
                  } else if (data.createdAt.toDate) {
                    order.createdAt = data.createdAt.toDate().toISOString();
                  } else {
                    order.createdAt = new Date().toISOString();
                  }
                } catch (e) {
                  order.createdAt = new Date().toISOString();
                }
              } else {
                order.createdAt = new Date().toISOString();
              }

              if (data.updatedAt) {
                try {
                  if (typeof data.updatedAt === 'string') {
                    order.updatedAt = data.updatedAt;
                  } else if (data.updatedAt.toDate) {
                    order.updatedAt = data.updatedAt.toDate().toISOString();
                  } else {
                    order.updatedAt = new Date().toISOString();
                  }
                } catch (e) {
                  order.updatedAt = new Date().toISOString();
                }
              } else {
                order.updatedAt = new Date().toISOString();
              }

              orders.push(order);
              console.log('✅ JavaScript: تم إضافة طلب:', order.id);
            } else {
              console.log('⚠️ JavaScript: مستند فارغ:', doc.id);
            }
          });

          console.log(`📦 JavaScript: تم جلب ${orders.length} طلب من Firestore`);

          // طباعة تفاصيل أول 3 طلبات للتحقق
          for (let i = 0; i < Math.min(orders.length, 3); i++) {
            console.log(`📋 JavaScript: طلب ${i + 1}:`, {
              id: orders[i].id,
              customerName: orders[i].customerName,
              status: orders[i].status,
              total: orders[i].total
            });
          }

          // حفظ في Cache
          this.cachedOrders = orders;
          console.log(`💾 JavaScript: تم حفظ ${orders.length} طلب في Cache`);

          return orders;
        } catch (error) {
          console.error('❌ JavaScript: خطأ في جلب الطلبات:', error);
          return this.cachedOrders || [];
        } finally {
          this.isLoading = false;
        }
      },

      // جلب الطلبات (النسخة القديمة للتوافق)
      async getOrders() {
        return await this.getOrdersAsync();
      },

      // إضافة طلب جديد باستخدام معرف واضح
      async addOrder(orderData) {
        const maxRetries = 3;
        let lastError;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
          try {
            console.log(`🔄 JavaScript: محاولة ${attempt}/${maxRetries} لإضافة طلب جديد`);
            console.log('🔄 JavaScript: معرف الطلب الواضح:', orderData.id);
            console.log('🔄 JavaScript: بيانات الطلب:', JSON.stringify(orderData));

            if (!orderData || !orderData.id) {
              throw new Error('بيانات الطلب غير صحيحة');
            }

            // انتظار قصير قبل المحاولة
            if (attempt > 1) {
              console.log(`⏳ JavaScript: انتظار ${attempt * 1000}ms قبل المحاولة...`);
              await new Promise(resolve => setTimeout(resolve, attempt * 1000));
            }

            console.log('🔄 JavaScript: إنشاء مرجع المجموعة...');
            const ordersCollection = db.collection('orders');
            console.log('🔄 JavaScript: تم إنشاء مرجع المجموعة');

            // استخدام معرف الطلب الواضح كمعرف المستند
            console.log('🔄 JavaScript: بدء إضافة المستند بمعرف واضح...');
            await ordersCollection.doc(orderData.id).set(orderData);
            console.log('🔄 JavaScript: تم إضافة المستند بنجاح');

            console.log('✅ JavaScript: تم إضافة الطلب بنجاح');
            console.log('✅ JavaScript: معرف المستند:', orderData.id);
            console.log('✅ JavaScript: معرف الطلب:', orderData.id);

            // مسح Cache لإجبار إعادة جلب البيانات
            this.cachedOrders = [];
            console.log('🗑️ JavaScript: تم مسح Cache الطلبات');

            return orderData.id; // إرجاع نفس المعرف الواضح
          } catch (error) {
            lastError = error;
            console.error(`❌ JavaScript: فشلت المحاولة ${attempt}/${maxRetries}:`, error.message);

            if (attempt === maxRetries) {
              console.error('❌ JavaScript: فشلت جميع المحاولات');
              console.error('❌ JavaScript: آخر خطأ:', error);
              throw error;
            }
          }
        }
      },

      // تحديث حالة الطلب باستخدام المعرف الواضح
      async updateOrderStatus(orderId, newStatus) {
        try {
          console.log('🔄 JavaScript: تحديث حالة الطلب بالمعرف الواضح:', orderId, 'إلى:', newStatus);

          if (!orderId || !newStatus) {
            throw new Error('معرف الطلب أو الحالة الجديدة فارغ');
          }

          // استخدام المعرف الواضح مباشرة
          const orderRef = db.collection('orders').doc(orderId);
          await orderRef.update({
            status: newStatus,
            updatedAt: new Date().toISOString()
          });

          console.log('✅ JavaScript: تم تحديث حالة الطلب بنجاح للمعرف:', orderId);

          // مسح Cache لإجبار إعادة جلب البيانات
          this.cachedOrders = [];
          console.log('🗑️ JavaScript: تم مسح Cache الطلبات');

          return true;
        } catch (error) {
          console.error('❌ JavaScript: خطأ في تحديث حالة الطلب:', orderId, error);
          throw error;
        }
      }


    };

    console.log('🔥 Firebase initialized successfully');
    console.log('🛠️ firebaseHelpers متاح الآن');

    // طباعة الدوال المتاحة للتحقق
    console.log('🔍 JavaScript: الدوال المتاحة في firebaseHelpers:');
    console.log('🔍 JavaScript: addOrder متاح:', typeof window.firebaseHelpers.addOrder);
    console.log('🔍 JavaScript: updateOrderStatus متاح:', typeof window.firebaseHelpers.updateOrderStatus);
    console.log('🔍 JavaScript: getOrdersAsync متاح:', typeof window.firebaseHelpers.getOrdersAsync);
  </script>

  <script>
    // Hide loading screen when Flutter is ready
    window.addEventListener('flutter-first-frame', function() {
      const loading = document.getElementById('loading');
      if (loading) {
        loading.style.display = 'none';
      }
    });
  </script>

  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
