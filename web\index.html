<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="VisionLens - متجر النظارات والعدسات اللاصقة">

  <!-- Google Sign-In Configuration -->
  <!-- ⚠️ استبدل CLIENT_ID بالقيمة الحقيقية من Google Cloud Console -->
  <meta name="google-signin-client_id" content="627749384715-lj6fcgr8pku7ajphb1859qsq894mlplb.apps.googleusercontent.com">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="VisionLens">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>VisionLens</title>
  <link rel="manifest" href="manifest.json">

  <style>
    body {
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
      font-family: 'Roboto', sans-serif;
    }

    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      flex-direction: column;
    }

    .loading-spinner {
      width: 50px;
      height: 50px;
      border: 4px solid #e3f2fd;
      border-top: 4px solid #2196f3;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .loading-text {
      margin-top: 20px;
      color: #2196f3;
      font-size: 16px;
    }
  </style>
</head>
<body>
  <div id="loading" class="loading">
    <div class="loading-spinner"></div>
    <div class="loading-text">جاري تحميل VisionLens...</div>
  </div>

  <!-- Firebase SDK -->
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-auth-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-firestore-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-storage-compat.js"></script>

  <!-- Google Sign-In -->
  <script src="https://accounts.google.com/gsi/client" async defer></script>

  <!-- Firebase Configuration -->
  <script>
    // Firebase configuration
    const firebaseConfig = {
      apiKey: "AIzaSyDkc8p_soWCQzm91z2zsYoPUyDpk21WdFw",
      authDomain: "visionlens-app-5ab70.firebaseapp.com",
      projectId: "visionlens-app-5ab70",
      storageBucket: "visionlens-app-5ab70.appspot.com",
      messagingSenderId: "************",
      appId: "1:************:web:a7cb9386ef6d04c7fe67ec"
    };

    // Initialize Firebase
    firebase.initializeApp(firebaseConfig);
    const db = firebase.firestore();

    console.log('🔥 Firebase initialized successfully');

    // دوال Firebase للاستخدام من Flutter
    window.firebaseHelpers = {
      // متغير لحفظ المنتجات والفئات والمستخدمين والطلبات والبراندات
      cachedProducts: [],
      cachedCategories: [],
      cachedUsers: [],
      cachedOrders: [],
      cachedBrands: [],
      isLoading: false,

      // مسح جميع البيانات المحفوظة
      clearCache() {
        console.log('🗑️ JavaScript: مسح جميع البيانات المحفوظة...');
        this.cachedProducts = [];
        this.cachedCategories = [];
        this.cachedUsers = [];
        this.cachedOrders = [];
        this.cachedBrands = [];
        this.isLoading = false;
        console.log('✅ JavaScript: تم مسح جميع البيانات المحفوظة');
      },

      // جلب المنتجات مع Promise صحيح
      async getProductsAsync() {
        if (this.isLoading) {
          console.log('⏳ JavaScript: جلب المنتجات قيد التنفيذ...');
          return this.cachedProducts;
        }

        this.isLoading = true;
        try {
          console.log('🔄 JavaScript: جلب المنتجات من Firestore...');
          const snapshot = await db.collection('products').get();
          const products = [];
          snapshot.forEach(doc => {
            products.push({ id: doc.id, ...doc.data() });
          });
          console.log(`📦 JavaScript: تم جلب ${products.length} منتج`);

          // حفظ المنتجات في المتغير العام
          this.cachedProducts = products;
          window.latestProducts = products;

          console.log(`💾 JavaScript: تم حفظ ${this.cachedProducts.length} منتج في Cache`);

          return products;
        } catch (error) {
          console.error('❌ JavaScript: خطأ في جلب المنتجات:', error);
          this.cachedProducts = [];
          return [];
        } finally {
          this.isLoading = false;
        }
      },

      // جلب المنتجات (النسخة القديمة للتوافق)
      async getProducts() {
        return await this.getProductsAsync();
      },

      // جلب المنتجات المحفوظة
      getCachedProducts() {
        console.log(`📦 JavaScript: إرجاع ${this.cachedProducts.length} منتج محفوظ`);
        return this.cachedProducts;
      },

      // جلب الفئات مع Promise صحيح
      getCategories() {
        console.log('🔄 JavaScript: جلب الفئات من Firestore...');

        return new Promise(async (resolve, reject) => {
          try {
            const snapshot = await db.collection('categories').get();
            const categories = [];
            snapshot.forEach(doc => {
              categories.push({ id: doc.id, ...doc.data() });
            });
            console.log(`📦 JavaScript: تم جلب ${categories.length} فئة`);

            // حفظ في Cache
            this.cachedCategories = categories;
            console.log(`💾 JavaScript: تم حفظ ${categories.length} فئة في Cache`);

            resolve(categories);
          } catch (error) {
            console.error('❌ JavaScript: خطأ في جلب الفئات:', error);
            reject(error);
          }
        });
      },

      // جلب الفئات من Cache
      getCachedCategories() {
        console.log(`📦 JavaScript: إرجاع ${this.cachedCategories ? this.cachedCategories.length : 0} فئة من Cache`);
        return this.cachedCategories || [];
      },

      // جلب المستخدمين من Cache
      getCachedUsers() {
        console.log(`👥 JavaScript: إرجاع ${this.cachedUsers ? this.cachedUsers.length : 0} مستخدم من Cache`);
        return this.cachedUsers || [];
      },

      // جلب الطلبات من Cache
      getCachedOrders() {
        console.log(`📦 JavaScript: إرجاع ${this.cachedOrders ? this.cachedOrders.length : 0} طلب من Cache`);
        return this.cachedOrders || [];
      },

      // جلب المستخدمين مع Promise صحيح
      getUsers() {
        console.log('🔄 JavaScript: جلب المستخدمين من Firestore...');

        return new Promise(async (resolve, reject) => {
          try {
            const snapshot = await db.collection('users').get();
            const users = [];
            snapshot.forEach(doc => {
              users.push({ id: doc.id, ...doc.data() });
            });
            console.log(`👥 JavaScript: تم جلب ${users.length} مستخدم`);

            // حفظ في Cache
            this.cachedUsers = users;
            console.log(`💾 JavaScript: تم حفظ ${users.length} مستخدم في Cache`);

            resolve(users);
          } catch (error) {
            console.error('❌ JavaScript: خطأ في جلب المستخدمين:', error);
            reject(error);
          }
        });
      },

      // جلب الطلبات مع Promise صحيح (النسخة الجديدة)
      async getOrdersAsync() {
        // مسح Cache بالكامل لإجبار إعادة جلب البيانات
        console.log('🗑️ JavaScript: مسح Cache الطلبات بالكامل...');
        this.cachedOrders = [];
        this.isLoading = false;

        this.isLoading = true;
        try {
          console.log('🔄 JavaScript: جلب الطلبات الجديدة من Firestore...');

          const snapshot = await db.collection('orders').get();
          const orders = [];

          snapshot.forEach(doc => {
            const data = doc.data();

            // طباعة تفاصيل كل طلب للتحقق من المعرفات
            console.log('🔍 JavaScript: معالجة طلب:');
            console.log('  📋 معرف المستند الحقيقي (doc.id):', JSON.stringify(doc.id));
            console.log('  📋 معرف في البيانات (data.id):', JSON.stringify(data.id));
            console.log('  📋 معرف في البيانات (data.orderId):', JSON.stringify(data.orderId));
            console.log('  📋 جميع الحقول:', JSON.stringify(Object.keys(data)));
            console.log('  📋 جميع البيانات:', data);
            console.log('  📋 جميع البيانات:', JSON.stringify(data, null, 2));

            orders.push({
              id: doc.id, // استخدام معرف المستند الحقيقي من Firestore
              firestoreId: doc.id, // حفظ معرف Firestore الحقيقي
              originalDataId: data.id, // حفظ المعرف من البيانات إن وجد (للمرجع فقط)
              displayId: data.id || data.orderId || doc.id, // معرف للعرض فقط
              ...data,
              // التأكد من تحويل التواريخ بشكل صحيح
              createdAt: data.createdAt ? data.createdAt.toDate().toISOString() : new Date().toISOString(),
              updatedAt: data.updatedAt ? data.updatedAt.toDate().toISOString() : new Date().toISOString()
            });
          });

          console.log(`📦 JavaScript: تم جلب ${orders.length} طلب من Firestore`);

          // طباعة تفاصيل أول 3 طلبات للتحقق
          for (let i = 0; i < Math.min(orders.length, 3); i++) {
            console.log(`📋 JavaScript: طلب ${i + 1}:`, {
              id: orders[i].id,
              customerName: orders[i].customerName,
              status: orders[i].status,
              total: orders[i].total
            });
          }

          // حفظ في Cache
          this.cachedOrders = orders;
          console.log(`💾 JavaScript: تم حفظ ${orders.length} طلب في Cache`);

          return orders;
        } catch (error) {
          console.error('❌ JavaScript: خطأ في جلب الطلبات:', error);
          return this.cachedOrders || [];
        } finally {
          this.isLoading = false;
        }
      },

      // جلب الطلبات (النسخة القديمة للتوافق)
      async getOrders() {
        return await this.getOrdersAsync();
      },

      // إضافة طلب جديد
      async addOrder(orderData) {
        try {
          console.log('🔄 JavaScript: إضافة طلب جديد:', orderData.id);

          const ordersCollection = db.collection('orders');
          const docRef = await ordersCollection.add(orderData);

          console.log('✅ JavaScript: تم إضافة الطلب بنجاح:', docRef.id);

          // مسح Cache لإجبار إعادة جلب البيانات
          this.cachedOrders = [];

          return docRef.id;
        } catch (error) {
          console.error('❌ JavaScript: خطأ في إضافة الطلب:', error);
          throw error;
        }
      },

      // تحديث حالة الطلب
      async updateOrderStatus(orderId, newStatus) {
        try {
          console.log('🔄 JavaScript: بدء تحديث حالة الطلب');
          console.log('🔄 JavaScript: معرف الطلب الأصلي:', orderId);
          console.log('🔄 JavaScript: الحالة الجديدة:', newStatus);
          console.log('🔄 JavaScript: نوع معرف الطلب:', typeof orderId);

          // تنظيف معرف الطلب وتحويله إلى string
          const cleanOrderId = String(orderId).trim();
          console.log('🔄 JavaScript: معرف الطلب المنظف:', cleanOrderId);
          console.log('🔄 JavaScript: طول معرف الطلب:', cleanOrderId.length);

          if (!cleanOrderId || !newStatus) {
            console.error('❌ JavaScript: معرف الطلب أو الحالة الجديدة فارغ');
            throw new Error('معرف الطلب أو الحالة الجديدة فارغ');
          }

          // التحقق من نوع معرف الطلب
          const isNumericId = /^\d+$/.test(cleanOrderId);
          const isOrderId = /^ORD\d+$/.test(cleanOrderId);
          const isFirestoreId = /^[a-zA-Z0-9]{20}$/.test(cleanOrderId); // معرف Firestore الحقيقي

          console.log('🔍 JavaScript: معرف رقمي فقط:', isNumericId);
          console.log('🔍 JavaScript: معرف بصيغة ORD:', isOrderId);
          console.log('🔍 JavaScript: معرف Firestore حقيقي:', isFirestoreId);

          if (!isNumericId && !isOrderId && !isFirestoreId) {
            console.warn('⚠️ JavaScript: معرف الطلب بصيغة غير متوقعة:', cleanOrderId);
          }

          console.log('🔄 JavaScript: إنشاء مرجع المستند...');
          const docRef = db.collection('orders').doc(cleanOrderId);

          console.log('🔄 JavaScript: تم إنشاء مرجع المستند للطلب:', cleanOrderId);

          // التحقق من وجود المستند
          console.log('🔄 JavaScript: التحقق من وجود المستند...');
          const docSnapshot = await docRef.get();
          console.log('🔄 JavaScript: نتيجة التحقق - المستند موجود:', docSnapshot.exists);

          if (!docSnapshot.exists) {
            console.error('❌ JavaScript: المستند غير موجود:', cleanOrderId);

            // البحث في جميع الطلبات للعثور على المعرف الصحيح
            console.log('🔍 JavaScript: البحث في جميع الطلبات...');
            const allOrdersSnapshot = await db.collection('orders').get();
            console.log('🔍 JavaScript: عدد الطلبات الموجودة:', allOrdersSnapshot.size);

            let foundOrder = null;
            allOrdersSnapshot.forEach(doc => {
              console.log('🔍 JavaScript: فحص طلب:', doc.id);
              if (doc.id === cleanOrderId) {
                foundOrder = doc;
                console.log('✅ JavaScript: تم العثور على الطلب المطابق:', doc.id);
              }
            });

            if (!foundOrder) {
              // محاولة البحث بطرق مختلفة
              console.log('🔍 JavaScript: محاولة البحث بطرق مختلفة...');
              allOrdersSnapshot.forEach(doc => {
                const docId = doc.id;
                console.log('🔍 JavaScript: مقارنة:', docId, 'مع', cleanOrderId);

                // مقارنة مباشرة
                if (docId === cleanOrderId) {
                  foundOrder = doc;
                  console.log('✅ JavaScript: تطابق مباشر:', docId);
                }
                // مقارنة بدون مسافات
                else if (docId.trim() === cleanOrderId.trim()) {
                  foundOrder = doc;
                  console.log('✅ JavaScript: تطابق بعد إزالة المسافات:', docId);
                }
                // مقارنة case-insensitive
                else if (docId.toLowerCase() === cleanOrderId.toLowerCase()) {
                  foundOrder = doc;
                  console.log('✅ JavaScript: تطابق case-insensitive:', docId);
                }
              });

              if (!foundOrder) {
                throw new Error(`الطلب ${cleanOrderId} غير موجود في قاعدة البيانات`);
              }
            }

            // استخدام المستند الموجود
            console.log('✅ JavaScript: تم العثور على الطلب، استخدام المعرف:', foundOrder.id);
            const correctDocRef = db.collection('orders').doc(foundOrder.id);
            const currentData = foundOrder.data();
            console.log('🔄 JavaScript: البيانات الحالية للطلب:', currentData);

            // تحديث المستند الصحيح
            console.log('🔄 JavaScript: بدء عملية التحديث للمستند الصحيح...');
            const updateData = {
              status: newStatus,
              updatedAt: new Date()
            };
            console.log('🔄 JavaScript: بيانات التحديث:', updateData);

            await correctDocRef.update(updateData);
            console.log('✅ JavaScript: تم تحديث المستند بنجاح');

            // التحقق من التحديث
            const updatedDoc = await correctDocRef.get();
            if (updatedDoc.exists) {
              const updatedData = updatedDoc.data();
              console.log('✅ JavaScript: البيانات بعد التحديث:', updatedData);
              console.log('✅ JavaScript: الحالة الجديدة المحفوظة:', updatedData.status);

              if (updatedData.status === newStatus) {
                console.log('✅ JavaScript: تأكيد - تم تحديث الحالة بنجاح إلى:', newStatus);

                // تحديث Cache المحلي
                if (this.cachedOrders && Array.isArray(this.cachedOrders)) {
                  const orderIndex = this.cachedOrders.findIndex(order =>
                    order.id === cleanOrderId || order.id === foundOrder.id
                  );
                  if (orderIndex !== -1) {
                    this.cachedOrders[orderIndex].status = newStatus;
                    this.cachedOrders[orderIndex].updatedAt = new Date().toISOString();
                    console.log('✅ JavaScript: تم تحديث Cache المحلي');
                  }
                }

                return true;
              } else {
                console.error('❌ JavaScript: فشل التحديث - الحالة المتوقعة:', newStatus, 'الحالة الفعلية:', updatedData.status);
                throw new Error('فشل في تحديث الحالة في قاعدة البيانات');
              }
            }
          } else {
            // المستند موجود، تحديث عادي
            const currentData = docSnapshot.data();
            console.log('🔄 JavaScript: البيانات الحالية للطلب:', currentData);

            // تحديث المستند
            console.log('🔄 JavaScript: بدء عملية التحديث...');
            const updateData = {
              status: newStatus,
              updatedAt: new Date()
            };
            console.log('🔄 JavaScript: بيانات التحديث:', updateData);

            await docRef.update(updateData);
            console.log('✅ JavaScript: تم تحديث المستند بنجاح');

            // التحقق من التحديث
            console.log('🔄 JavaScript: التحقق من التحديث...');
            const updatedDoc = await docRef.get();
            if (updatedDoc.exists) {
              const updatedData = updatedDoc.data();
              console.log('✅ JavaScript: البيانات بعد التحديث:', updatedData);
              console.log('✅ JavaScript: الحالة الجديدة المحفوظة:', updatedData.status);

              // التحقق من أن الحالة تم تحديثها فعلاً
              if (updatedData.status === newStatus) {
                console.log('✅ JavaScript: تأكيد - تم تحديث الحالة بنجاح إلى:', newStatus);

                // تحديث Cache المحلي
                if (this.cachedOrders && Array.isArray(this.cachedOrders)) {
                  const orderIndex = this.cachedOrders.findIndex(order => order.id === cleanOrderId);
                  if (orderIndex !== -1) {
                    this.cachedOrders[orderIndex].status = newStatus;
                    this.cachedOrders[orderIndex].updatedAt = new Date().toISOString();
                    console.log('✅ JavaScript: تم تحديث Cache المحلي');
                  }
                }

                return true;
              } else {
                console.error('❌ JavaScript: فشل التحديث - الحالة المتوقعة:', newStatus, 'الحالة الفعلية:', updatedData.status);
                throw new Error('فشل في تحديث الحالة في قاعدة البيانات');
              }
            } else {
              console.error('❌ JavaScript: المستند غير موجود بعد التحديث');
              throw new Error('المستند غير موجود بعد التحديث');
            }
          }
        } catch (error) {
          console.error('❌ JavaScript: خطأ في تحديث حالة الطلب:', error);
          console.error('❌ JavaScript: نوع الخطأ:', typeof error);
          console.error('❌ JavaScript: رسالة الخطأ:', error.message);
          console.error('❌ JavaScript: تفاصيل الخطأ:', error);
          throw error;
        }
      }
    };

    console.log('🔥 Firebase initialized successfully');
    console.log('🛠️ firebaseHelpers متاح الآن');
  </script>

  <script>
    // Hide loading screen when Flutter is ready
    window.addEventListener('flutter-first-frame', function() {
      const loading = document.getElementById('loading');
      if (loading) {
        loading.style.display = 'none';
      }
    });
  </script>

  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
