import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/product.dart';
import '../models/category.dart';

/// خدمة إدارة البيانات الحقيقية للتطبيق
/// تستخدم SharedPreferences لحفظ البيانات محلياً
/// يمكن استبدالها بقاعدة بيانات حقيقية لاحقاً
class DataService {
  static const String _productsKey = 'visionlens_products';
  static const String _categoriesKey = 'visionlens_categories';
  static const String _usersKey = 'visionlens_users';
  static const String _ordersKey = 'visionlens_orders';

  // ==================== إدارة المنتجات ====================

  /// حفظ قائمة المنتجات
  static Future<void> saveProducts(List<Product> products) async {
    final prefs = await SharedPreferences.getInstance();
    final productsJson = products.map((p) => p.toJson()).toList();
    await prefs.setString(_productsKey, jsonEncode(productsJson));
  }

  /// جلب قائمة المنتجات
  static Future<List<Product>> getProducts() async {
    final prefs = await SharedPreferences.getInstance();
    final productsString = prefs.getString(_productsKey);

    if (productsString == null) {
      // إضافة منتجات تجريبية مع صور حقيقية
      await _initializeDefaultProducts();
      return await getProducts();
    }

    final List<dynamic> productsJson = jsonDecode(productsString);
    return productsJson.map((json) => Product.fromJson(json)).toList();
  }

  /// إضافة منتجات افتراضية مع صور حقيقية
  static Future<void> _initializeDefaultProducts() async {
    final defaultProducts = [
      Product(
        id: '1749340935409',
        name: 'عدسات لاصقة زرقاء',
        description: 'عدسات لاصقة ملونة زرقاء عالية الجودة',
        price: 25000,
        image:
            'https://images.unsplash.com/photo-1574258495973-f010dfbb5371?w=500&h=500&fit=crop',
        images: [
          'https://images.unsplash.com/photo-1574258495973-f010dfbb5371?w=500&h=500&fit=crop',
        ],
        categoryId: '1',
        categoryName: 'عدسات لاصقة',
        rating: 4.5,
        reviewsCount: 25,
        inStock: true,
        stockQuantity: 50,
        specifications: {},
        type: ProductType.contactLenses,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        stock: 50,
      ),
      Product(
        id: '1749345475993',
        name: 'عدسات خضراء',
        description: 'عدسات لاصقة ملونة خضراء طبيعية',
        price: 30000,
        image:
            'https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=500&h=500&fit=crop',
        images: [
          'https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=500&h=500&fit=crop',
        ],
        categoryId: '1',
        categoryName: 'عدسات لاصقة',
        rating: 4.8,
        reviewsCount: 42,
        inStock: true,
        stockQuantity: 30,
        specifications: {},
        type: ProductType.contactLenses,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        stock: 30,
      ),
      Product(
        id: '1749346166823',
        name: 'عدسات ملونة',
        description: 'مجموعة متنوعة من العدسات الملونة',
        price: 35000,
        image:
            'https://images.unsplash.com/photo-1508296695146-257a814070b4?w=500&h=500&fit=crop',
        images: [
          'https://images.unsplash.com/photo-1508296695146-257a814070b4?w=500&h=500&fit=crop',
        ],
        categoryId: '1',
        categoryName: 'عدسات لاصقة',
        rating: 4.3,
        reviewsCount: 18,
        inStock: true,
        stockQuantity: 25,
        specifications: {},
        type: ProductType.contactLenses,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        stock: 25,
      ),
    ];

    await saveProducts(defaultProducts);
  }

  /// إضافة منتج جديد
  static Future<void> addProduct(Product product) async {
    final products = await getProducts();
    products.add(product);
    await saveProducts(products);
  }

  /// تحديث منتج موجود
  static Future<void> updateProduct(Product product) async {
    final products = await getProducts();
    final index = products.indexWhere((p) => p.id == product.id);

    if (index != -1) {
      products[index] = product;
      await saveProducts(products);
    }
  }

  /// حذف منتج
  static Future<void> deleteProduct(String productId) async {
    final products = await getProducts();
    products.removeWhere((p) => p.id == productId);
    await saveProducts(products);
  }

  /// البحث في المنتجات
  static Future<List<Product>> searchProducts(String query) async {
    final products = await getProducts();

    if (query.isEmpty) {
      return products;
    }

    return products.where((product) {
      return product.name.toLowerCase().contains(query.toLowerCase()) ||
          product.description.toLowerCase().contains(query.toLowerCase()) ||
          (product.brand?.toLowerCase().contains(query.toLowerCase()) ?? false);
    }).toList();
  }

  /// جلب المنتجات حسب الفئة
  static Future<List<Product>> getProductsByCategory(String categoryId) async {
    final products = await getProducts();
    return products.where((p) => p.categoryId == categoryId).toList();
  }

  /// جلب المنتجات المميزة
  static Future<List<Product>> getFeaturedProducts() async {
    final products = await getProducts();
    return products.where((p) => p.isFeatured).toList();
  }

  /// جلب المنتجات الجديدة
  static Future<List<Product>> getNewProducts() async {
    final products = await getProducts();
    final now = DateTime.now();
    final thirtyDaysAgo = now.subtract(const Duration(days: 30));

    return products.where((p) => p.createdAt.isAfter(thirtyDaysAgo)).toList();
  }

  // ==================== إدارة الفئات ====================

  /// حفظ قائمة الفئات
  static Future<void> saveCategories(List<Category> categories) async {
    final prefs = await SharedPreferences.getInstance();
    final categoriesJson = categories.map((c) => c.toJson()).toList();
    await prefs.setString(_categoriesKey, jsonEncode(categoriesJson));
  }

  /// جلب قائمة الفئات
  static Future<List<Category>> getCategories() async {
    final prefs = await SharedPreferences.getInstance();
    final categoriesString = prefs.getString(_categoriesKey);

    if (categoriesString == null) {
      return [];
    }

    final List<dynamic> categoriesJson = jsonDecode(categoriesString);
    return categoriesJson.map((json) => Category.fromJson(json)).toList();
  }

  /// إضافة فئة جديدة
  static Future<void> addCategory(Category category) async {
    final categories = await getCategories();
    categories.add(category);
    await saveCategories(categories);
  }

  /// تحديث فئة موجودة
  static Future<void> updateCategory(Category category) async {
    final categories = await getCategories();
    final index = categories.indexWhere((c) => c.id == category.id);

    if (index != -1) {
      categories[index] = category;
      await saveCategories(categories);
    }
  }

  /// حذف فئة
  static Future<void> deleteCategory(String categoryId) async {
    final categories = await getCategories();
    categories.removeWhere((c) => c.id == categoryId);
    await saveCategories(categories);
  }

  // ==================== إدارة المستخدمين ====================

  /// حفظ قائمة المستخدمين
  static Future<void> saveUsers(List<Map<String, dynamic>> users) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_usersKey, jsonEncode(users));
  }

  /// جلب قائمة المستخدمين
  static Future<List<Map<String, dynamic>>> getUsers() async {
    final prefs = await SharedPreferences.getInstance();
    final usersString = prefs.getString(_usersKey);

    if (usersString == null) {
      return [];
    }

    final List<dynamic> usersJson = jsonDecode(usersString);
    return usersJson.cast<Map<String, dynamic>>();
  }

  /// إضافة مستخدم جديد
  static Future<void> addUser(Map<String, dynamic> user) async {
    final users = await getUsers();
    users.add(user);
    await saveUsers(users);
  }

  /// تحديث مستخدم موجود
  static Future<void> updateUser(Map<String, dynamic> user) async {
    final users = await getUsers();
    final index = users.indexWhere((u) => u['id'] == user['id']);

    if (index != -1) {
      users[index] = user;
      await saveUsers(users);
    }
  }

  /// حذف مستخدم
  static Future<void> deleteUser(String userId) async {
    final users = await getUsers();
    users.removeWhere((u) => u['id'] == userId);
    await saveUsers(users);
  }

  // ==================== إدارة الطلبات ====================

  /// حفظ قائمة الطلبات
  static Future<void> saveOrders(List<Map<String, dynamic>> orders) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_ordersKey, jsonEncode(orders));
  }

  /// جلب قائمة الطلبات
  static Future<List<Map<String, dynamic>>> getOrders() async {
    final prefs = await SharedPreferences.getInstance();
    final ordersString = prefs.getString(_ordersKey);

    if (ordersString == null) {
      return [];
    }

    final List<dynamic> ordersJson = jsonDecode(ordersString);
    return ordersJson.cast<Map<String, dynamic>>();
  }

  /// إضافة طلب جديد
  static Future<void> addOrder(Map<String, dynamic> order) async {
    final orders = await getOrders();
    orders.add(order);
    await saveOrders(orders);
  }

  /// تحديث طلب موجود
  static Future<void> updateOrder(Map<String, dynamic> order) async {
    final orders = await getOrders();
    final index = orders.indexWhere((o) => o['id'] == order['id']);

    if (index != -1) {
      // تنظيف البيانات وتحويل DateTime إلى String بشكل شامل
      final cleanOrder = _cleanOrderData(order);

      orders[index] = cleanOrder;
      await saveOrders(orders);
    }
  }

  /// تنظيف بيانات الطلب وتحويل DateTime إلى String
  static Map<String, dynamic> _cleanOrderData(Map<String, dynamic> order) {
    final cleanOrder = <String, dynamic>{};

    for (final entry in order.entries) {
      final key = entry.key;
      final value = entry.value;

      if (value is DateTime) {
        // تحويل DateTime إلى String
        cleanOrder[key] = value.toIso8601String();
      } else if (value is List) {
        // تنظيف القوائم
        cleanOrder[key] = value.map((item) {
          if (item is Map<String, dynamic>) {
            return _cleanOrderData(item);
          } else if (item is DateTime) {
            return item.toIso8601String();
          }
          return item;
        }).toList();
      } else if (value is Map<String, dynamic>) {
        // تنظيف الخرائط المتداخلة
        cleanOrder[key] = _cleanOrderData(value);
      } else if (value is! Function) {
        // إضافة القيم العادية (ما عدا Functions)
        cleanOrder[key] = value;
      }
    }

    return cleanOrder;
  }

  /// حذف طلب
  static Future<void> deleteOrder(String orderId) async {
    final orders = await getOrders();
    orders.removeWhere((o) => o['id'] == orderId);
    await saveOrders(orders);
  }

  // ==================== إدارة البراندات ====================

  /// حفظ قائمة البراندات
  static Future<void> saveBrands(List<Map<String, dynamic>> brands) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('brands', jsonEncode(brands));
  }

  /// جلب قائمة البراندات
  static Future<List<Map<String, dynamic>>> getBrands() async {
    final prefs = await SharedPreferences.getInstance();
    final brandsString = prefs.getString('brands');

    if (brandsString == null) {
      // إنشاء براندات افتراضية
      final defaultBrands = [
        {
          'id': '1',
          'name': 'Acuvue',
          'description': 'براند عالمي للعدسات اللاصقة',
          'createdAt': DateTime.now().toIso8601String(),
          'updatedAt': DateTime.now().toIso8601String(),
        },
        {
          'id': '2',
          'name': 'Ray-Ban',
          'description': 'براند مشهور للنظارات الشمسية',
          'createdAt': DateTime.now().toIso8601String(),
          'updatedAt': DateTime.now().toIso8601String(),
        },
        {
          'id': '3',
          'name': 'Oakley',
          'description': 'براند رياضي للنظارات',
          'createdAt': DateTime.now().toIso8601String(),
          'updatedAt': DateTime.now().toIso8601String(),
        },
      ];
      await saveBrands(defaultBrands);
      return defaultBrands;
    }

    final List<dynamic> brandsJson = jsonDecode(brandsString);
    return brandsJson.cast<Map<String, dynamic>>();
  }

  /// إضافة براند جديد
  static Future<void> addBrand(Map<String, dynamic> brand) async {
    final brands = await getBrands();
    brands.add(brand);
    await saveBrands(brands);
  }

  /// تحديث براند موجود
  static Future<void> updateBrand(Map<String, dynamic> brand) async {
    final brands = await getBrands();
    final index = brands.indexWhere((b) => b['id'] == brand['id']);

    if (index != -1) {
      brands[index] = brand;
      await saveBrands(brands);
    }
  }

  /// حذف براند
  static Future<void> deleteBrand(String brandId) async {
    final brands = await getBrands();
    brands.removeWhere((b) => b['id'] == brandId);
    await saveBrands(brands);
  }

  // ==================== إدارة عامة ====================

  /// مسح جميع البيانات
  static Future<void> clearAllData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_productsKey);
    await prefs.remove(_categoriesKey);
    await prefs.remove(_usersKey);
    await prefs.remove(_ordersKey);
  }

  /// تصدير البيانات
  static Future<Map<String, dynamic>> exportData() async {
    return {
      'products': await getProducts(),
      'categories': await getCategories(),
      'users': await getUsers(),
      'orders': await getOrders(),
      'exportDate': DateTime.now().toIso8601String(),
    };
  }
}
