{"hosting": {"public": "build/web", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**/*.@(eot|otf|ttf|ttc|woff|font.css)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}]}, {"source": "**/*.@(js|css)", "headers": [{"key": "Cache-Control", "value": "max-age=604800"}]}, {"source": "**/*.@(jpg|jpeg|gif|png|svg|webp)", "headers": [{"key": "Cache-Control", "value": "max-age=2592000"}]}], "cleanUrls": true, "trailingSlash": false}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "storage": {"rules": "storage.rules"}, "emulators": {"auth": {"port": 9099}, "firestore": {"port": 8080}, "hosting": {"port": 5000}, "storage": {"port": 9199}, "ui": {"enabled": true, "port": 4000}}}